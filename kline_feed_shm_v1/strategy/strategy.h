#include <nlohmann_json/json.hpp>
#include <vector>
#include <queue>
#include <unordered_map>
#include <unordered_set>

#include "../../frame/strategy_logger.h"
#include "../data/data_manager.h"
#include "../data/shm_kline_manager.h"
#include "fast_trader_elite/data_model/field.h"
#include "i_strategy.h"
#include "cpp_frame/utils/file_helper.h"

namespace fast_trader_elite::strategy {

class strategy : public i_strategy {
public:
  strategy();
  virtual ~strategy();
  virtual bool on_start(i_strategy_ctx *ctx,
                        const std::string &strategy_name) override;
  virtual bool on_stop(i_strategy_ctx *ctx) override;
  virtual void on_kline_data(i_strategy_ctx *ctx,
                             kline_market_data_field *field) override;
  virtual void on_depth_data(i_strategy_ctx *ctx,
                             depth_market_data_field *field) override;
  virtual void on_tick_data(i_strategy_ctx *ctx,
                            tick_market_data_field *field) override;
  virtual void on_transaction_data(i_strategy_ctx *ctx,
                                   transaction_field *field) override;
  virtual void on_liquidation_data(i_strategy_ctx *ctx,
                                   liquidation_field *field) override;

  virtual void on_http_depth_data(i_strategy_ctx *ctx,
                                  depth_market_data_field *field,
                                  bool is_last) override;
  virtual void on_http_tick_data(i_strategy_ctx *ctx,
                                 tick_market_data_field *field,
                                 bool is_last) override;
  virtual void on_http_liquidation_data(i_strategy_ctx *ctx,
                                        liquidation_field *field,
                                        bool is_last) override;
  virtual void on_http_kline_data(i_strategy_ctx *ctx,
                                  kline_market_data_field *field,
                                  bool is_last) override;
  virtual void on_http_transaction_data(i_strategy_ctx *ctx,
                                        transaction_field *field,
                                        bool is_last) override;
  virtual void on_trade_proxy_state(i_strategy_ctx *ctx,
                                    td_proxy_state_field *field) override;
  virtual void on_order(i_strategy_ctx *ctx, order_field *field) override;
  virtual void on_trade(i_strategy_ctx *ctx, trade_field *field) override;
  virtual void on_rtn_position(i_strategy_ctx *ctx,
                               position_field *position) override;
  virtual void
  on_rtn_wallet_balance(i_strategy_ctx *ctx,
                        wallet_balance_field *wallet_balance) override;
  virtual void on_http_rsp(i_strategy_ctx *ctx, http_rsp_field *field) override;

private:
  void parse_config(const std::string &config_path);
  void perpare_strategy();
  void subscribe();
  void process_next_batch();
  void check_initialization_completion();
  void process_cached_klines();
    void check_flush(uint64_t current_time);

private:
  int async_timer_id_;
  fast_trader_elite::strategy::logger logger_;
  data_manager *data_{nullptr};
  shm_kline_manager *kline_manager_{nullptr};
  i_strategy_ctx *ctx_;
  nlohmann::json config_json_;

  // 用于跟踪K线数据接收情况
  std::unordered_map<std::string, bool> received_klines_;  // 记录每个合约是否接收到K线
  std::unordered_map<std::string, uint64_t> last_update_times_;  // 记录每个合约最后更新K线的时间
  uint64_t current_kline_timestamp_{0};  // 当前K线时间戳
  int total_instruments_{0};  // 合约总数
  int received_instruments_{0};  // 已接收到K线的合约数

  // 分批请求K线相关
  int batch_size_{20};  // 每批请求的合约数量
  std::queue<data*> pending_contracts_;  // 待请求的合约队列
  std::unordered_set<int> pending_request_ids_;  // 当前批次中待响应的请求ID集合
  int next_request_id_{1000};  // 下一个请求ID
  bool is_batch_processing_{false};  // 是否正在处理批量请求

  // 存储每个合约的最新时间戳
  std::unordered_map<std::string, uint64_t> contract_latest_timestamps_;

  // 初始化阶段的数据缓存
  struct KlineCache {
    std::vector<kline_market_data_field> http_klines;  // HTTP获取的K线数据
    std::vector<kline_market_data_field> ws_klines;    // WebSocket获取的K线数据
  };
  std::unordered_map<std::string, KlineCache> init_kline_cache_;  // 初始化阶段的K线缓存
  bool initialization_completed_{false};                          // 初始化是否已完成
  std::vector<kline_market_data_field> klines_;

  int check_timer_id_{-1};
  uint64_t last_kline_close_time_{0}; //本轮K线收盘时间
  uint64_t last_write_time_{0};

  cpp_frame::file_helper file_helper_;
};
} // namespace fast_trader_elite::strategy