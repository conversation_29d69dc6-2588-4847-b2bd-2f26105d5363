#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警管理器
支持邮件和钉钉告警功能
"""

import smtplib
import requests
import json
import time
import logging
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from typing import Optional, Dict, Any


class AlertManager:
    """告警管理器，支持邮件和钉钉告警"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化告警管理器
        
        Args:
            config: 告警配置字典，包含邮件和钉钉配置
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 邮件配置
        self.email_config = self.config.get('email', {})
        self.email_enabled = self.email_config.get('enabled', False)
        
        # 钉钉配置
        self.dingtalk_config = self.config.get('dingtalk', {})
        self.dingtalk_enabled = self.dingtalk_config.get('enabled', False)
        
        # 告警频率限制（避免频繁告警）
        self.alert_interval = self.config.get('alert_interval', 300)  # 5分钟
        self.last_alert_time = {}
        
        self.logger.info(f"告警管理器初始化完成，邮件: {'启用' if self.email_enabled else '禁用'}, "
                        f"钉钉: {'启用' if self.dingtalk_enabled else '禁用'}")
    
    def send_email_alert(self, subject: str, content: str, alert_type: str = "warning") -> bool:
        """
        发送邮件告警
        
        Args:
            subject: 邮件主题
            content: 邮件内容
            alert_type: 告警类型
            
        Returns:
            bool: 发送是否成功
        """
        if not self.email_enabled:
            self.logger.debug("邮件告警未启用，跳过发送")
            return False
            
        try:
            # 检查告警频率限制
            if not self._should_send_alert("email", alert_type):
                return False
                
            smtp_server = self.email_config.get('smtp_server')
            smtp_port = self.email_config.get('smtp_port', 587)
            username = self.email_config.get('username')
            password = self.email_config.get('password')
            from_addr = self.email_config.get('from_addr', username)
            to_addrs = self.email_config.get('to_addrs', [])
            
            if not all([smtp_server, username, password, to_addrs]):
                self.logger.error("邮件配置不完整，无法发送告警")
                return False
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = Header(f"K线监控系统 <{from_addr}>", 'utf-8')
            msg['To'] = Header(', '.join(to_addrs), 'utf-8')
            msg['Subject'] = Header(f"[{alert_type.upper()}] {subject}", 'utf-8')
            
            # 添加时间戳和系统信息
            full_content = f"""
告警时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
告警类型: {alert_type.upper()}
系统: K线数据处理监控系统

{content}

---
此邮件由K线监控系统自动发送，请勿回复。
"""
            
            msg.attach(MIMEText(full_content, 'plain', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP_SSL(smtp_server, smtp_port)
            server.login(username, password)
            server.sendmail(from_addr, to_addrs, msg.as_string())
            server.quit()
            
            self.logger.info(f"邮件告警发送成功: {subject}")
            self._update_last_alert_time("email", alert_type)
            return True
            
        except Exception as e:
            self.logger.error(f"发送邮件告警失败: {e}")
            return False
    
    def send_dingtalk_alert(self, title: str, content: str, alert_type: str = "warning") -> bool:
        """
        发送钉钉告警
        
        Args:
            title: 告警标题
            content: 告警内容
            alert_type: 告警类型
            
        Returns:
            bool: 发送是否成功
        """
        if not self.dingtalk_enabled:
            self.logger.debug("钉钉告警未启用，跳过发送")
            return False
            
        try:
            # 检查告警频率限制
            if not self._should_send_alert("dingtalk", alert_type):
                return False
                
            webhook_url = self.dingtalk_config.get('webhook_url')
            secret = self.dingtalk_config.get('secret')
            
            if not webhook_url:
                self.logger.error("钉钉webhook_url未配置，无法发送告警")
                return False
            
            # 构造钉钉消息
            timestamp = str(round(time.time() * 1000))
            
            # 如果配置了secret，需要计算签名
            if secret:
                import hmac
                import hashlib
                import base64
                import urllib.parse
                
                string_to_sign = f'{timestamp}\n{secret}'
                hmac_code = hmac.new(secret.encode('utf-8'), 
                                   string_to_sign.encode('utf-8'), 
                                   digestmod=hashlib.sha256).digest()
                sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
                webhook_url = f"{webhook_url}&timestamp={timestamp}&sign={sign}"
            
            # 根据告警类型选择颜色
            color_map = {
                "info": "🔵",
                "warning": "🟡", 
                "error": "🔴",
                "critical": "🚨"
            }
            icon = color_map.get(alert_type, "⚠️")
            
            message = {
                "msgtype": "markdown",
                "markdown": {
                    "title": f"{icon} {title}",
                    "text": f"""## {icon} {title}

**告警时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**告警类型**: {alert_type.upper()}

**详细信息**:
{content}

---
> K线数据处理监控系统自动告警
"""
                }
            }
            
            # 发送请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(webhook_url, 
                                   data=json.dumps(message), 
                                   headers=headers, 
                                   timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.logger.info(f"钉钉告警发送成功: {title}")
                    self._update_last_alert_time("dingtalk", alert_type)
                    return True
                else:
                    self.logger.error(f"钉钉告警发送失败: {result.get('errmsg')}")
                    return False
            else:
                self.logger.error(f"钉钉告警发送失败，HTTP状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送钉钉告警失败: {e}")
            return False
    
    def send_alert(self, title: str, content: str, alert_type: str = "warning") -> bool:
        """
        发送告警（同时发送邮件和钉钉）
        
        Args:
            title: 告警标题
            content: 告警内容
            alert_type: 告警类型 (info, warning, error, critical)
            
        Returns:
            bool: 至少一种方式发送成功
        """
        email_success = self.send_email_alert(title, content, alert_type)
        dingtalk_success = self.send_dingtalk_alert(title, content, alert_type)
        
        success = email_success or dingtalk_success
        
        if success:
            self.logger.info(f"告警发送完成: {title} (邮件: {'成功' if email_success else '失败'}, "
                           f"钉钉: {'成功' if dingtalk_success else '失败'})")
        else:
            self.logger.error(f"所有告警方式都发送失败: {title}")
            
        return success
    
    def _should_send_alert(self, channel: str, alert_type: str) -> bool:
        """检查是否应该发送告警（频率限制）"""
        key = f"{channel}_{alert_type}"
        current_time = time.time()
        
        if key in self.last_alert_time:
            time_diff = current_time - self.last_alert_time[key]
            if time_diff < self.alert_interval:
                self.logger.debug(f"告警频率限制，跳过发送: {key} (距离上次 {time_diff:.1f}秒)")
                return False
        
        return True
    
    def _update_last_alert_time(self, channel: str, alert_type: str):
        """更新最后告警时间"""
        key = f"{channel}_{alert_type}"
        self.last_alert_time[key] = time.time()
    
    @classmethod
    def create_default_config(cls) -> Dict[str, Any]:
        """创建默认配置模板"""
        return {
            "email": {
                "enabled": False,
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "<EMAIL>",
                "password": "your_app_password",
                "from_addr": "<EMAIL>",
                "to_addrs": ["<EMAIL>"]
            },
            "dingtalk": {
                "enabled": False,
                "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN",
                "secret": "YOUR_SECRET"  # 可选，用于签名验证
            },
            "alert_interval": 300  # 告警间隔（秒）
        }


# 使用示例
if __name__ == "__main__":
    # 创建配置示例
    config = AlertManager.create_default_config()
    
    # 启用钉钉告警（示例）
    config['dingtalk']['enabled'] = True
    config['dingtalk']['webhook_url'] = "YOUR_WEBHOOK_URL"
    
    # 创建告警管理器
    alert_manager = AlertManager(config)
    
    # 发送测试告警
    alert_manager.send_alert(
        title="K线监控系统测试",
        content="这是一条测试告警消息",
        alert_type="info"
    )
