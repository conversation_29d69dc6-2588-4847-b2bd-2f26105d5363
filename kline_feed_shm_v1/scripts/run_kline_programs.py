#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线数据处理程序调度脚本
按严格顺序运行三个程序，并监控共享内存更新状态

功能特性:
1. 程序调度：auto_instrument -> fix_md_tools -> kline_feed_shm_v1
2. 共享内存监控：检测kline_data.shm文件的更新状态
3. 自动重启：当检测到超过64秒无更新时，自动重启所有程序
4. 进程管理：优雅的进程启停和异常处理

运行顺序:
1. auto_instrument.sh: 自动配置工具脚本（运行完成后退出）
2. fix_md_tools: 修复共享内存中的K线数据（运行完成后退出）
3. kline_feed_shm_v1: K线数据落地到共享内存（常驻进程）

作者: 自动生成
日期: 2024
"""

import os
import sys
import subprocess
import time
import signal
import threading
import ctypes
import mmap
import argparse
import logging
import json
from datetime import datetime
from alert_manager import AlertManager

# 程序状态枚举
class ShmProgramState:
    INIT = 0      # 初始化状态
    RUNNING = 1   # 正常运行状态
    STOP = 2      # 停止状态
    ERROR = 3     # 错误状态

    @staticmethod
    def to_string(state):
        state_map = {
            ShmProgramState.INIT: "INIT",
            ShmProgramState.RUNNING: "RUNNING",
            ShmProgramState.STOP: "STOP",
            ShmProgramState.ERROR: "ERROR"
        }
        return state_map.get(state, f"UNKNOWN({state})")

# 定义与C++相同的数据结构
class KlineShmHeader(ctypes.Structure):
    _fields_ = [
        ("magic", ctypes.c_uint32),
        ("version", ctypes.c_uint32),
        ("max_contracts", ctypes.c_uint32),
        ("max_klines_per_contract", ctypes.c_uint32),
        ("contract_count", ctypes.c_uint32),
        ("last_kline_close_time", ctypes.c_uint64),
        ("last_update_timestamp", ctypes.c_uint64),
        ("program_state", ctypes.c_uint32),  # 新增：程序状态字段
        ("reserved", ctypes.c_uint32 * 5)   # 减少为5个reserved字段
    ]

class KlineProgramRunner:
    def __init__(self):
        # 配置路径变量 - 可以根据需要修改这些路径
        self.git_root = "/home/<USER>/git"
        self.fast_trader_elite_path = os.path.join(self.git_root, "fast_trader_elite_pkg/main/fast_trader_elite")

        # auto_instrument相关路径
        self.auto_instrument_dir = os.path.join(self.git_root, "fast_trader_elite_pkg/auto_instrument")
        self.auto_instrument_script = os.path.join(self.auto_instrument_dir, "auto_instrument.sh")

        # kline_feed_shm_v1相关路径
        self.kline_feed_config = os.path.join(self.git_root, "fast_trader_elite_pkg/config/kline_feed_shm.json")

        # fix_md_tools相关路径
        self.fix_md_config = os.path.join(self.git_root, "fast_trader_elite_pkg/config/fix_md.json")

        # 日志目录
        self.log_dir = os.path.join(self.git_root, "fast_trader_elite_pkg/log/scripts")

        # 共享内存文件路径
        self.shm_file_path = os.path.join(self.git_root, "fast_trader_elite_pkg/main/kline_data.shm")

        # 进程管理
        self.auto_instrument_process = None
        self.kline_feed_process = None
        self.fix_md_process = None
        self.running = False

        # 监控相关
        self.check_flush_enabled = True
        self.flush_timeout_seconds = 64  # 64秒超时
        self.monitor_thread = None

        # 告警管理器
        self.alert_manager = None
        self.alert_config_path = os.path.join(self.git_root, "fast_trader_elite_pkg/strategy_config/kline_alert_config.json")

        # 设置日志
        self.setup_logging()

        # 初始化告警管理器
        self.init_alert_manager()
        
    def setup_logging(self):
        """设置日志配置"""
        # 确保日志目录存在
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 配置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(os.path.join(self.log_dir, f'runner_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def check_paths(self):
        """检查所有必要的路径是否存在"""
        paths_to_check = [
            ("fast_trader_elite可执行文件", self.fast_trader_elite_path),
            ("auto_instrument脚本", self.auto_instrument_script),
            ("auto_instrument目录", self.auto_instrument_dir),
            ("kline_feed配置文件", self.kline_feed_config),
            ("fix_md配置文件", self.fix_md_config)
        ]

        missing_paths = []
        for name, path in paths_to_check:
            if not os.path.exists(path):
                missing_paths.append(f"{name}: {path}")

        if missing_paths:
            self.logger.error("以下路径不存在:")
            for path in missing_paths:
                self.logger.error(f"  - {path}")
            return False

        self.logger.info("所有路径检查通过")
        return True

    def init_alert_manager(self):
        """初始化告警管理器"""
        try:
            # 尝试加载告警配置
            if os.path.exists(self.alert_config_path):
                with open(self.alert_config_path, 'r', encoding='utf-8') as f:
                    alert_config = json.load(f)
                self.logger.info(f"加载告警配置文件: {self.alert_config_path}")
            else:
                # 使用默认配置
                alert_config = AlertManager.create_default_config()
                self.logger.info("使用默认告警配置")

            self.alert_manager = AlertManager(alert_config)

        except Exception as e:
            self.logger.error(f"初始化告警管理器失败: {e}")
            # 创建一个禁用的告警管理器
            self.alert_manager = AlertManager({})

    def read_shm_header(self):
        """读取共享内存头部信息"""
        try:
            if not os.path.exists(self.shm_file_path):
                return None

            # 使用mmap读取共享内存，与demo保持一致
            fd = os.open(self.shm_file_path, os.O_RDONLY)
            shm = mmap.mmap(fd, 0, mmap.MAP_SHARED, mmap.PROT_READ)
            os.close(fd)

            try:
                # 读取头部信息
                header_size = ctypes.sizeof(KlineShmHeader)
                header_data = shm[:header_size]
                header = KlineShmHeader.from_buffer_copy(header_data)

                # 解析程序状态（使用专门的program_state字段）
                program_state = header.program_state if header.program_state <= 3 else ShmProgramState.ERROR

                return {
                    'magic': header.magic,
                    'version': header.version,
                    'max_contracts': header.max_contracts,
                    'max_klines_per_contract': header.max_klines_per_contract,
                    'contract_count': header.contract_count,
                    'last_kline_close_time': header.last_kline_close_time,
                    'last_update_timestamp': header.last_update_timestamp,
                    'program_state': program_state,
                    'program_state_str': ShmProgramState.to_string(program_state)
                }
            finally:
                shm.close()

        except Exception as e:
            self.logger.error(f"读取共享内存头部失败: {e}")
            return None

    def check_shm_update_timeout(self):
        """检查共享内存更新是否超时"""
        header = self.read_shm_header()
        if header is None:
            self.logger.warning("无法读取共享内存头部，可能文件不存在")
            return False

        # 验证魔数
        expected_magic = 0x4B4C494E  # "KLIN" in ASCII
        if header['magic'] != expected_magic:
            self.logger.warning(f"共享内存魔数不匹配，期望: {expected_magic:x}, 实际: {header['magic']:x}")
            return False

        # 检查程序状态
        program_state = header['program_state']
        program_state_str = header['program_state_str']

        self.logger.debug(f"共享内存程序状态: {program_state_str}")

        # 如果程序状态为INIT，不检测超时
        if program_state == ShmProgramState.INIT:
            self.logger.debug("程序状态为INIT，跳过超时检测")
            return False

        # 如果程序状态为STOP或ERROR，也不检测超时（避免误报）
        if program_state in [ShmProgramState.STOP, ShmProgramState.ERROR]:
            self.logger.debug(f"程序状态为{program_state_str}，跳过超时检测")
            return False

        # 获取当前时间（毫秒）
        current_time_ms = int(time.time() * 1000)
        last_kline_close_time_ms = header['last_kline_close_time']

        # 如果last_kline_close_time为0，说明还没有数据更新
        if last_kline_close_time_ms == 0:
            self.logger.debug("共享内存last_kline_close_time为0，可能还没有数据")
            return False

        # 计算时间差（秒）
        time_diff_seconds = (current_time_ms - last_kline_close_time_ms) / 1000.0

        self.logger.debug(f"共享内存K线收盘时间差: {time_diff_seconds:.2f}秒 (当前: {current_time_ms}, K线收盘: {last_kline_close_time_ms}, 状态: {program_state_str})")

        # 只有在RUNNING状态下才检查超时
        if program_state == ShmProgramState.RUNNING and time_diff_seconds > self.flush_timeout_seconds:
            self.logger.warning(f"共享内存K线更新超时: {time_diff_seconds:.2f}秒 > {self.flush_timeout_seconds}秒 (状态: {program_state_str})")
            return True

        return False

    def run_auto_instrument(self):
        """运行auto_instrument.sh脚本"""
        try:
            cmd = ["sh","auto_instrument.sh"]
            log_file = os.path.join(self.log_dir, f'auto_instrument_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

            self.logger.info(f"运行auto_instrument.sh脚本...")
            self.logger.info(f"命令: {' '.join(cmd)}")
            self.logger.info(f"工作目录: {self.auto_instrument_dir}")
            self.logger.info(f"日志文件: {log_file}")

            with open(log_file, 'w') as f:
                self.auto_instrument_process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    cwd=self.auto_instrument_dir
                )

            self.logger.info(f"auto_instrument.sh脚本已启动，PID: {self.auto_instrument_process.pid}")

            # 等待脚本执行完成
            return_code = self.auto_instrument_process.wait()

            if return_code == 0:
                self.logger.info("auto_instrument.sh脚本执行成功")
                return True
            else:
                self.logger.error(f"auto_instrument.sh脚本执行失败，退出码: {return_code}")
                return False

        except Exception as e:
            self.logger.error(f"运行auto_instrument.sh脚本失败: {e}")
            return False

    def run_kline_feed_shm(self):
        """运行kline_feed_shm_v1程序"""
        try:
            cmd = [self.fast_trader_elite_path, self.kline_feed_config]
            log_file = os.path.join(self.log_dir, f'kline_feed_shm_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            
            self.logger.info(f"启动kline_feed_shm_v1程序...")
            self.logger.info(f"命令: {' '.join(cmd)}")
            self.logger.info(f"日志文件: {log_file}")
            
            with open(log_file, 'w') as f:
                self.kline_feed_process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    cwd=os.path.dirname(self.fast_trader_elite_path)
                )
                
            self.logger.info(f"kline_feed_shm_v1程序已启动，PID: {self.kline_feed_process.pid}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动kline_feed_shm_v1程序失败: {e}")
            return False
            
    def run_fix_md_tools(self):
        """运行fix_md_tools程序并等待完成"""
        try:
            cmd = [self.fast_trader_elite_path, self.fix_md_config]
            log_file = os.path.join(self.log_dir, f'fix_md_tools_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

            self.logger.info(f"运行fix_md_tools程序...")
            self.logger.info(f"命令: {' '.join(cmd)}")
            self.logger.info(f"日志文件: {log_file}")

            with open(log_file, 'w') as f:
                self.fix_md_process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    cwd=os.path.dirname(self.fast_trader_elite_path)
                )

            self.logger.info(f"fix_md_tools程序已启动，PID: {self.fix_md_process.pid}")

            # 等待程序执行完成
            return_code = self.fix_md_process.wait()

            if return_code == 0:
                self.logger.info("fix_md_tools程序执行成功")
                return True
            else:
                self.logger.error(f"fix_md_tools程序执行失败，退出码: {return_code}")
                return False

        except Exception as e:
            self.logger.error(f"运行fix_md_tools程序失败: {e}")
            return False
            
    def stop_processes(self):
        """停止所有进程"""
        self.logger.info("正在停止所有进程...")
        self.running = False

        processes = [
            ("auto_instrument", self.auto_instrument_process),
            ("kline_feed_shm_v1", self.kline_feed_process),
            ("fix_md_tools", self.fix_md_process)
        ]
        
        for name, process in processes:
            if process and process.poll() is None:
                try:
                    self.logger.info(f"停止{name}程序 (PID: {process.pid})")
                    process.terminate()
                    
                    # 等待进程优雅退出
                    try:
                        process.wait(timeout=10)
                        self.logger.info(f"{name}程序已正常退出")
                    except subprocess.TimeoutExpired:
                        self.logger.warning(f"{name}程序未在10秒内退出，强制终止")
                        process.kill()
                        process.wait()
                        
                except Exception as e:
                    self.logger.error(f"停止{name}程序时出错: {e}")
                    
    def monitor_processes(self):
        """监控进程状态和共享内存更新"""
        while self.running:
            try:
                # 检查auto_instrument进程（通常已经完成）
                if self.auto_instrument_process and self.auto_instrument_process.poll() is not None:
                    if self.auto_instrument_process.returncode != 0:
                        self.logger.warning(f"auto_instrument程序退出，退出码: {self.auto_instrument_process.returncode}")

                # 检查kline_feed_shm进程
                kline_feed_running = (self.kline_feed_process and
                                    self.kline_feed_process.poll() is None)

                if self.kline_feed_process and self.kline_feed_process.poll() is not None:
                    self.logger.warning(f"kline_feed_shm_v1程序意外退出，退出码: {self.kline_feed_process.returncode}")
                    kline_feed_running = False

                # 检查fix_md_tools进程（通常已经完成）
                if self.fix_md_process and self.fix_md_process.poll() is not None:
                    if self.fix_md_process.returncode != 0:
                        self.logger.warning(f"fix_md_tools程序退出，退出码: {self.fix_md_process.returncode}")

                # 如果kline_feed_shm正在运行且启用了flush检查，检查共享内存更新
                if (kline_feed_running and self.check_flush_enabled and
                    self.check_shm_update_timeout()):

                    self.logger.error("检测到共享内存更新超时，准备重启所有程序")

                    # 发送告警通知
                    self.send_timeout_alert()

                    # 停止当前的kline_feed_shm进程
                    if self.kline_feed_process:
                        try:
                            self.logger.info("终止kline_feed_shm_v1进程")
                            self.kline_feed_process.terminate()
                            try:
                                self.kline_feed_process.wait(timeout=10)
                            except subprocess.TimeoutExpired:
                                self.logger.warning("kline_feed_shm_v1进程未在10秒内退出，强制终止")
                                self.kline_feed_process.kill()
                                self.kline_feed_process.wait()
                        except Exception as e:
                            self.logger.error(f"终止kline_feed_shm_v1进程时出错: {e}")

                    # 重启所有程序
                    self.logger.info("开始重启所有程序...")
                    restart_success = self.restart_all_programs()

                    # 发送重启结果通知
                    self.send_restart_result_alert(restart_success)

                    if not restart_success:
                        self.logger.error("重启程序失败，停止监控")
                        self.running = False
                        break

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                self.logger.error(f"监控进程时出错: {e}")
                time.sleep(5)

    def restart_all_programs(self):
        """重启所有程序"""
        try:
            self.logger.info("=== 重启程序：第1步 - 运行auto_instrument.sh ===")
            if not self.run_auto_instrument():
                return False

            self.logger.info("=== 重启程序：第2步 - 运行fix_md_tools ===")
            if not self.run_fix_md_tools():
                return False

            self.logger.info("=== 重启程序：第3步 - 运行kline_feed_shm_v1 ===")
            if not self.run_kline_feed_shm():
                return False

            self.logger.info("所有程序重启完成")
            return True

        except Exception as e:
            self.logger.error(f"重启程序时出错: {e}")
            return False

    def send_timeout_alert(self):
        """发送超时告警"""
        try:
            # 获取共享内存状态信息
            header = self.read_shm_header()
            if header:
                current_time_ms = int(time.time() * 1000)
                time_diff_seconds = (current_time_ms - header['last_kline_close_time']) / 1000.0

                title = "K线数据更新超时告警"
                content = f"""
**告警详情**:
- 超时阈值: {self.flush_timeout_seconds}秒
- 实际时间差: {time_diff_seconds:.2f}秒
- 程序状态: {header.get('program_state_str', 'UNKNOWN')}
- 合约数量: {header.get('contract_count', 0)}
- 最后K线时间: {datetime.fromtimestamp(header['last_kline_close_time']/1000).strftime('%Y-%m-%d %H:%M:%S')}

**处理措施**:
系统将自动重启所有相关程序以恢复正常运行。

**系统信息**:
- 共享内存文件: {self.shm_file_path}
- Git根目录: {self.git_root}
"""
            else:
                title = "K线数据更新超时告警"
                content = f"""
**告警详情**:
- 超时阈值: {self.flush_timeout_seconds}秒
- 无法读取共享内存状态

**处理措施**:
系统将自动重启所有相关程序以恢复正常运行。
"""

            if self.alert_manager:
                self.alert_manager.send_alert(title, content, "error")

        except Exception as e:
            self.logger.error(f"发送超时告警失败: {e}")

    def send_restart_result_alert(self, success: bool):
        """发送重启结果告警"""
        try:
            if success:
                title = "K线程序重启成功"
                content = f"""
**重启结果**: ✅ 成功

**重启时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**重启流程**:
1. auto_instrument.sh - 完成
2. fix_md_tools - 完成
3. kline_feed_shm_v1 - 已启动

系统已恢复正常运行，继续监控中。
"""
                alert_type = "info"
            else:
                title = "K线程序重启失败"
                content = f"""
**重启结果**: ❌ 失败

**重启时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**影响**:
- 监控系统已停止
- K线数据处理可能中断
- 需要人工介入处理

**建议措施**:
1. 检查系统日志
2. 手动重启相关程序
3. 验证配置文件正确性
"""
                alert_type = "critical"

            if self.alert_manager:
                self.alert_manager.send_alert(title, content, alert_type)

        except Exception as e:
            self.logger.error(f"发送重启结果告警失败: {e}")

    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在退出...")
        self.stop_processes()
        sys.exit(0)
        
    def run_all_programs(self):
        """按顺序运行所有程序：auto_instrument -> fix_md -> kline_feed_shm"""
        if not self.check_paths():
            return False

        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        self.running = True

        # 第一步：运行auto_instrument.sh脚本（等待完成）
        self.logger.info("=== 第1步：运行auto_instrument.sh脚本 ===")
        if not self.run_auto_instrument():
            self.logger.error("auto_instrument.sh脚本执行失败，停止后续操作")
            return False

        self.logger.info("auto_instrument.sh脚本执行完成")

        # 第二步：运行fix_md_tools程序（等待完成）
        self.logger.info("=== 第2步：运行fix_md_tools程序 ===")
        if not self.run_fix_md_tools():
            self.logger.error("fix_md_tools程序执行失败，停止后续操作")
            return False

        self.logger.info("fix_md_tools程序执行完成")

        # 第三步：运行kline_feed_shm_v1程序（常驻进程）
        self.logger.info("=== 第3步：运行kline_feed_shm_v1程序（常驻进程）===")
        if not self.run_kline_feed_shm():
            self.logger.error("kline_feed_shm_v1程序启动失败")
            return False

        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        self.monitor_thread.start()

        self.logger.info("所有程序已按顺序启动完成，kline_feed_shm_v1正在运行")
        self.logger.info(f"共享内存监控已启用，超时阈值: {self.flush_timeout_seconds}秒")
        self.logger.info("按Ctrl+C退出")

        try:
            # 主线程等待
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")

        self.stop_processes()
        return True
        
    def send_alert(self, title, content, alert_type):
        """发送告警"""
        if self.alert_manager:
            self.alert_manager.send_alert(title, content, alert_type)

    def run_single_program(self, program_type):
        """运行单个程序"""
        if not self.check_paths():
            return False

        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        self.running = True

        if program_type == "auto_instrument":
            success = self.run_auto_instrument()
            # auto_instrument是同步执行的，不需要等待
            return success
        elif program_type == "kline_feed":
            success = self.run_kline_feed_shm()
            process = self.kline_feed_process
        elif program_type == "fix_md":
            success = self.run_fix_md_tools()
            # fix_md是同步执行的，不需要等待
            return success
        else:
            self.logger.error(f"未知的程序类型: {program_type}")
            return False

        if not success:
            return False

        self.logger.info(f"程序已启动，按Ctrl+C退出")

        try:
            # 等待进程结束（只有kline_feed是常驻进程）
            process.wait()
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")

        self.stop_processes()
        return True


def main():
    parser = argparse.ArgumentParser(description='K线数据处理程序调度脚本')
    parser.add_argument('--mode', choices=['all', 'auto_instrument', 'kline_feed', 'fix_md'],
                       default='all', help='运行模式 (默认: all - 按顺序运行所有程序)')
    parser.add_argument('--git-root', default='/home/<USER>/git',
                       help='Git根目录路径 (默认: /home/<USER>/git)')
    parser.add_argument('--disable-flush-check', action='store_true',
                       help='禁用共享内存flush检查')
    parser.add_argument('--flush-timeout', type=int, default=64,
                       help='共享内存更新超时时间（秒，默认64秒）')
    parser.add_argument('--alert-config',
                       help='告警配置文件路径（可选）')

    args = parser.parse_args()

    runner = KlineProgramRunner()

    # 如果指定了不同的git根目录，更新路径
    if args.git_root != runner.git_root:
        runner.git_root = args.git_root
        runner.fast_trader_elite_path = os.path.join(runner.git_root, "fast_trader_elite_pkg/main/fast_trader_elite")
        runner.auto_instrument_dir = os.path.join(runner.git_root, "fast_trader_elite_pkg/auto_instrument")
        runner.auto_instrument_script = os.path.join(runner.auto_instrument_dir, "auto_instrument.sh")
        runner.kline_feed_config = os.path.join(runner.git_root, "cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json")
        runner.fix_md_config = os.path.join(runner.git_root, "fast_trader_elite_pkg/config/fix_md.json")
        runner.log_dir = os.path.join(runner.git_root, "cross_selction_strategy/kline_feed_shm_v1/log")
        runner.shm_file_path = os.path.join(runner.git_root, "fast_trader_elite_pkg/main/kline_data.shm")
        runner.setup_logging()

    # 设置flush检查参数
    runner.check_flush_enabled = not args.disable_flush_check
    runner.flush_timeout_seconds = args.flush_timeout

    # 设置告警配置路径
    if args.alert_config:
        runner.alert_config_path = args.alert_config
        runner.init_alert_manager()  # 重新初始化告警管理器

    if args.mode == 'all':
        success = runner.run_all_programs()
        runner.send_alert("系统启动", f"成功 {success}", "info")
    elif args.mode == 'auto_instrument':
        success = runner.run_single_program('auto_instrument')
    elif args.mode == 'kline_feed':
        success = runner.run_single_program('kline_feed')
    elif args.mode == 'fix_md':
        success = runner.run_single_program('fix_md')

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
