#include <iostream>
#include <iomanip>
#include <string>
#include <vector>
#include <fcntl.h>
#include <sys/mman.h>
#include <unistd.h>
#include <cstring>
#include <atomic>
#include <chrono>
#include <ctime>

// 常量定义
constexpr uint32_t KLINE_SHM_MAGIC = 0x4B4C494E;  // "KLIN" in ASCII
constexpr uint32_t KLINE_SHM_VERSION = 1;
constexpr uint32_t MAX_CONTRACTS = 1000;


// K线数据结构
struct kline_data {
    uint64_t timestamp;                // 时间戳
    double open;                       // 开盘价
    double high;                       // 最高价
    double low;                        // 最低价
    double close;                      // 收盘价
    double volume;                     // 成交量
};

// 合约元数据结构
struct contract_metadata {
    char instrument_name[64];          // 合约名称
    alignas(4) std::atomic<uint32_t> kline_count;  // 当前K线数量
    alignas(4) std::atomic<uint32_t> head_index;   // 环形缓冲区头部索引
    uint64_t last_timestamp;           // 最后更新时间戳
    uint32_t reserved[2];              // 保留字段
};

// 共享内存头部结构
struct kline_shm_header {
    uint32_t magic;                    // 魔数
    uint32_t version;                  // 版本号
    uint32_t max_contracts;            // 最大合约数量
    uint32_t max_klines_per_contract;  // 每个合约的最大K线数量
    uint32_t contract_count;           // 当前合约数量
    uint64_t last_update_time;         // 最后更新时间
    uint32_t reserved[8];              // 保留字段
};

// 共享内存总体结构
struct kline_shm {
    kline_shm_header header;                    // 头部信息
    contract_metadata contracts[MAX_CONTRACTS]; // 合约元数据数组
    kline_data data[];                          // K线数据柔性数组 [MAX_CONTRACTS * max_klines_per_contract]
};

// 将时间戳转换为可读的时间字符串
std::string timestamp_to_string(uint64_t timestamp) {
    time_t time = timestamp / 1000; // 毫秒转秒
    struct tm *tm_info = localtime(&time);
    char buffer[30];
    strftime(buffer, 30, "%Y-%m-%d %H:%M:%S", tm_info);
    return std::string(buffer);
}

// 打印结构体大小和偏移量信息
void print_struct_info() {
    std::cout << "结构体大小信息：" << std::endl;
    std::cout << "sizeof(kline_data): " << sizeof(kline_data) << " 字节" << std::endl;
    std::cout << "sizeof(contract_metadata): " << sizeof(contract_metadata) << " 字节" << std::endl;
    std::cout << "sizeof(kline_shm_header): " << sizeof(kline_shm_header) << " 字节" << std::endl;
    std::cout << "sizeof(kline_shm): " << sizeof(kline_shm) << " 字节" << std::endl;
    
    std::cout << "\n合约元数据字段偏移量：" << std::endl;
    std::cout << "offsetof(contract_metadata, instrument_name): " 
              << offsetof(contract_metadata, instrument_name) << std::endl;
    std::cout << "offsetof(contract_metadata, kline_count): " 
              << offsetof(contract_metadata, kline_count) << std::endl;
    std::cout << "offsetof(contract_metadata, head_index): " 
              << offsetof(contract_metadata, head_index) << std::endl;
    std::cout << "offsetof(contract_metadata, last_timestamp): " 
              << offsetof(contract_metadata, last_timestamp) << std::endl;
    std::cout << "offsetof(contract_metadata, reserved): " 
              << offsetof(contract_metadata, reserved) << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <共享内存文件路径> [合约名称]" << std::endl;
        return 1;
    }
    
    const char* shm_path = argv[1];
    std::string target_instrument = (argc > 2) ? argv[2] : "";
    
    // 打印结构体信息
    print_struct_info();
    
    // 打开共享内存文件
    int fd = open(shm_path, O_RDONLY);
    if (fd == -1) {
        std::cerr << "无法打开共享内存文件: " << shm_path << std::endl;
        return 1;
    }
    
    // 获取文件大小
    off_t file_size = lseek(fd, 0, SEEK_END);
    lseek(fd, 0, SEEK_SET);
    
    // 映射共享内存
    void* mapped_mem = mmap(nullptr, file_size, PROT_READ, MAP_SHARED, fd, 0);
    close(fd);
    
    if (mapped_mem == MAP_FAILED) {
        std::cerr << "内存映射失败" << std::endl;
        return 1;
    }
    
    // 转换为kline_shm结构体
    kline_shm* shm = static_cast<kline_shm*>(mapped_mem);
    
    // 验证魔数和版本
    if (shm->header.magic != KLINE_SHM_MAGIC) {
        std::cerr << "无效的共享内存格式，魔数不匹配: " << std::hex << shm->header.magic << std::endl;
        munmap(mapped_mem, file_size);
        return 1;
    }
    
    if (shm->header.version != KLINE_SHM_VERSION) {
        std::cout << "警告: 共享内存版本不匹配，期望 " << KLINE_SHM_VERSION 
                  << "，实际 " << shm->header.version << std::endl;
    }
    
    // 打印头部信息
    std::cout << "\n共享内存头部信息：" << std::endl;
    std::cout << "合约数量: " << shm->header.contract_count << std::endl;
    std::cout << "最后更新时间: " << timestamp_to_string(shm->header.last_update_time) << std::endl;
    
    // 打印合约信息
    std::cout << "\n合约信息：" << std::endl;
    for (uint32_t i = 0; i < shm->header.contract_count; ++i) {
        const contract_metadata& contract = shm->contracts[i];
        std::string instrument_name = contract.instrument_name;
        
        // 如果指定了目标合约且不匹配，则跳过
        if (!target_instrument.empty() && instrument_name != target_instrument) {
            continue;
        }
        
        uint32_t kline_count = contract.kline_count.load(std::memory_order_relaxed);
        uint32_t head_index = contract.head_index.load(std::memory_order_relaxed);
        
        std::cout << "合约: " << instrument_name << std::endl;
        std::cout << "  K线数量: " << kline_count << std::endl;
        std::cout << "  头部索引: " << head_index << std::endl;
        std::cout << "  最后时间戳: " << timestamp_to_string(contract.last_timestamp) << std::endl;
        
        // 打印K线数据（最多显示5条）
        uint32_t display_count = std::min(kline_count, 5u);
        if (display_count > 0) {
            std::cout << "  K线数据样例（最多5条）：" << std::endl;
            std::cout << "    时间戳\t\t开盘价\t\t最高价\t\t最低价\t\t收盘价\t\t成交量" << std::endl;
            
            for (uint32_t j = 0; j < display_count; ++j) {
                uint32_t idx = (head_index + j) % shm->header.max_klines_per_contract;
                // 计算在柔性数组中的位置
                size_t offset = i * shm->header.max_klines_per_contract + idx;
                const kline_data& kline = shm->data[offset];
                
                std::cout << "    " << timestamp_to_string(kline.timestamp) << "\t"
                          << std::fixed << std::setprecision(2)
                          << kline.open << "\t\t"
                          << kline.high << "\t\t"
                          << kline.low << "\t\t"
                          << kline.close << "\t\t"
                          << kline.volume << std::endl;
            }
        }
        
        std::cout << std::endl;
    }
    
    // 解除内存映射
    munmap(mapped_mem, file_size);
    
    return 0;
}