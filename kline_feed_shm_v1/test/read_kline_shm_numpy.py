"""
使用NumPy直接访问内存读取共享内存中的K线数据 - 高性能方案
"""

import ctypes
import mmap
import numpy as np
import pandas as pd
from datetime import datetime
import os
import argparse
import time

# 常量定义
KLINE_SHM_MAGIC = 0x4B4C494E  # "KLIN" in ASCII
KLINE_SHM_VERSION = 1
MAX_CONTRACTS = 1000
MAX_KLINES_PER_CONTRACT = 5000

# 使用ctypes定义与C++相同的数据结构
class KlineData(ctypes.Structure):
    _fields_ = [
        ("timestamp", ctypes.c_uint64),
        ("open", ctypes.c_double),
        ("high", ctypes.c_double),
        ("low", ctypes.c_double),
        ("close", ctypes.c_double),
        ("volume", ctypes.c_double)
    ]

class ContractMetadata(ctypes.Structure):
    _fields_ = [
        ("instrument_name", ctypes.c_char * 64),
        ("kline_count", ctypes.c_uint32),  # 原子类型，但在Python中仍然表示为uint32
        # ("_padding1", ctypes.c_uint32),    # 对齐填充
        ("head_index", ctypes.c_uint32),   # 原子类型，但在Python中仍然表示为uint32
        # ("_padding2", ctypes.c_uint32),    # 对齐填充
        ("last_timestamp", ctypes.c_uint64),
        ("reserved", ctypes.c_uint32 * 2)  # 保留字段减少为2个
    ]

class KlineShmHeader(ctypes.Structure):
    _fields_ = [
        ("magic", ctypes.c_uint32),
        ("version", ctypes.c_uint32),
        ("max_contracts", ctypes.c_uint32),
        ("max_klines_per_contract", ctypes.c_uint32),
        ("contract_count", ctypes.c_uint32),
        ("last_update_time", ctypes.c_uint64),
        ("reserved", ctypes.c_uint32 * 8)
    ]

class KlineReader:
    def __init__(self, shm_path):
        """
        初始化K线读取器

        参数:
            shm_path: 共享内存文件路径
        """
        self.shm_path = shm_path
        self.shm = None
        self.header = None
        self.contracts = []
        self.open_shm()

    def open_shm(self):
        """打开共享内存文件并读取元数据"""
        try:
            fd = os.open(self.shm_path, os.O_RDONLY)
            self.shm = mmap.mmap(fd, 0, mmap.MAP_SHARED, mmap.PROT_READ)
            os.close(fd)

            # 读取头部信息
            header_size = ctypes.sizeof(KlineShmHeader)
            header_data = self.shm[:header_size]
            self.header = KlineShmHeader.from_buffer_copy(header_data)

            # 验证魔数和版本
            if self.header.magic != KLINE_SHM_MAGIC:
                raise ValueError(f"无效的共享内存格式，魔数不匹配: {self.header.magic}")

            if self.header.version != KLINE_SHM_VERSION:
                print(f"警告: 共享内存版本不匹配，期望 {KLINE_SHM_VERSION}，实际 {self.header.version}")

            # 读取合约信息
            contract_size = ctypes.sizeof(ContractMetadata)
            for i in range(self.header.contract_count):
                offset = header_size + i * contract_size
                contract_data = self.shm[offset:offset + contract_size]
                contract = ContractMetadata.from_buffer_copy(contract_data)
                self.contracts.append(contract)

            print(f"成功打开共享内存，包含 {len(self.contracts)} 个合约")

        except Exception as e:
            print(f"打开共享内存失败: {e}")
            if hasattr(self, 'shm') and self.shm:
                self.shm.close()
                self.shm = None

    def get_contract_names(self):
        """获取所有合约名称"""
        if not self.contracts:
            return []

        names = []
        for contract in self.contracts:
            name = contract.instrument_name.decode('utf-8').rstrip('\0')
            names.append(name)
        return names

    def read_kline_data(self, instrument=None, fields=None):
        """
        读取K线数据 - 使用NumPy直接访问内存的高性能版本

        参数:
            instrument: 合约名称，如果为None则读取所有合约
            fields: 需要读取的字段列表，默认为['close', 'high', 'low', 'open', 'volume']

        返回:
            一个字典，键为字段名，值为DataFrame
        """
        if not self.header or not self.shm:
            return {}

        if fields is None:
            fields = ['close', 'high', 'low', 'open', 'volume']

        # 创建结果字典
        result = {field: {} for field in fields}

        # 字段到结构体偏移量的映射
        field_offsets = {
            'open': KlineData.open.offset,
            'high': KlineData.high.offset,
            'low': KlineData.low.offset,
            'close': KlineData.close.offset,
            'volume': KlineData.volume.offset
        }

        # 确定要读取的合约列表
        contract_indices = []
        if instrument is None:
            # 读取所有合约
            for i in range(len(self.contracts)):
                contract_indices.append(i)
        else:
            # 读取指定合约
            for i, contract in enumerate(self.contracts):
                name = contract.instrument_name.decode('utf-8').rstrip('\0')
                if name == instrument:
                    contract_indices.append(i)
                    break

        # 计算基础偏移量
        header_size = ctypes.sizeof(KlineShmHeader)
        contracts_size = ctypes.sizeof(ContractMetadata) * MAX_CONTRACTS
        kline_size = ctypes.sizeof(KlineData)

        # 读取每个合约的数据
        for idx in contract_indices:
            contract = self.contracts[idx]
            name = contract.instrument_name.decode('utf-8').rstrip('\0')

            kline_count = min(contract.kline_count, MAX_KLINES_PER_CONTRACT)
            if kline_count == 0:
                continue

            # 创建时间索引
            timestamps = np.zeros(kline_count, dtype=np.uint64)

            # 计算数据区域的起始位置
            data_start = header_size + contracts_size + (idx * MAX_KLINES_PER_CONTRACT * kline_size)

            # 读取每个字段的数据
            field_data = {field: np.zeros(kline_count, dtype=np.float64) for field in fields}

            for i in range(kline_count):
                # 计算环形缓冲区中的实际索引
                idx_in_buffer = (contract.head_index + i) % MAX_KLINES_PER_CONTRACT
                kline_offset = data_start + (idx_in_buffer * kline_size)

                # 读取时间戳
                timestamps[i] = ctypes.c_uint64.from_buffer_copy(self.shm, kline_offset).value

                # 读取各个字段的值
                for field in fields:
                    if field in field_offsets:
                        field_offset = kline_offset + field_offsets[field]
                        field_data[field][i] = ctypes.c_double.from_buffer_copy(self.shm, field_offset).value

            # 转换时间戳为datetime 非常耗时
            # datetime_index = [datetime.fromtimestamp(ts / 1000) for ts in timestamps]

            # 将数据添加到结果字典
            for field in fields:
                if field in field_data:
                    result[field][name] = pd.Series(field_data[field])

        # 将每个字段的数据转换为DataFrame
        for field in fields:
            if result[field]:
                result[field] = pd.DataFrame(result[field])

        return result

    def close(self):
        """关闭共享内存"""
        if self.shm:
            self.shm.close()
            self.shm = None
            self.header = None
            self.contracts = []

def main():
    parser = argparse.ArgumentParser(description='使用NumPy直接访问内存读取共享内存中的K线数据')
    parser.add_argument('--shm', type=str, default='/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm',
                        help='共享内存文件路径')
    parser.add_argument('--fields', type=str, default='close,high,volume,open,low',
                        help='需要读取的字段，用逗号分隔')
    parser.add_argument('--contract', type=str, default=None,
                        help='指定要读取的合约，默认读取所有合约')
    parser.add_argument('--save', action='store_true',
                        help='是否保存数据到CSV文件')
    parser.add_argument('--benchmark', action='store_true',
                        help='是否进行性能基准测试')
    parser.add_argument('--iterations', type=int, default=10,
                        help='基准测试的迭代次数')

    args = parser.parse_args()

    # 解析字段列表
    fields = args.fields.split(',')

    try:
        # 创建K线读取器
        reader = KlineReader(args.shm)

        if args.benchmark:
            # 进行性能基准测试
            print(f"进行性能基准测试，迭代次数: {args.iterations}")
            total_time = 0

            for i in range(args.iterations):
                start_time = time.time()
                data = reader.read_kline_data(args.contract, fields)
                end_time = time.time()

                iteration_time = end_time - start_time
                total_time += iteration_time

                print(f"迭代 {i+1}: {iteration_time:.6f} 秒")

            avg_time = total_time / args.iterations
            print(f"平均读取时间: {avg_time:.6f} 秒")

            # 打印数据形状
            for field in fields:
                if field in data and isinstance(data[field], pd.DataFrame) and not data[field].empty:
                    print(f"{field} 数据形状: {data[field].shape}")
        else:
            # 打印所有合约名称
            contract_names = reader.get_contract_names()
            print(f"共有 {len(contract_names)} 个合约:")
            for name in contract_names[:10]:
                print(f"  - {name}")
            if len(contract_names) > 10:
                print(f"  ... 以及其他 {len(contract_names) - 10} 个合约")

            # 读取K线数据
            start_time = time.time()
            data = reader.read_kline_data(args.contract, fields)
            end_time = time.time()
            print(f"读取耗时: {end_time - start_time:.6f} 秒")

            # 打印每个字段的数据形状
            for field in fields:
                if field in data and isinstance(data[field], pd.DataFrame) and not data[field].empty:
                    df = data[field]
                    print(f"\n{field} 数据形状: {df.shape}")
                    print(f"合约列表: {df.columns.tolist()[:5]}... (共 {len(df.columns)} 个)")
                    print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
                    print(f"样例数据:\n{df.iloc[-5:, :3]}")

                    # 如果指定了保存，将数据保存到CSV文件
                    if args.save:
                        csv_file = f"{field}_data.csv"
                        df.to_csv(csv_file)
                        print(f"数据已保存到 {csv_file}")
                else:
                    print(f"\n{field} 数据为空")

        # 关闭读取器
        reader.close()

    except Exception as e:
        print(f"读取数据时出错: {e}")

if __name__ == "__main__":
    main()
