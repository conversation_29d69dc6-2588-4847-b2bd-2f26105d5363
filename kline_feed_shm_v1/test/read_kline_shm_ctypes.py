"""
使用ctypes读取共享内存中的K线数据 - 基本映射方案
"""

import ctypes
import mmap
import numpy as np
import pandas as pd
from datetime import datetime
import os
import argparse
import time

# 常量定义
KLINE_SHM_MAGIC = 0x4B4C494E  # "KLIN" in ASCII
KLINE_SHM_VERSION = 1
MAX_CONTRACTS = 1000
MAX_KLINES_PER_CONTRACT = 5000

# 使用ctypes定义与C++相同的数据结构
class KlineData(ctypes.Structure):
    _fields_ = [
        ("timestamp", ctypes.c_uint64),
        ("open", ctypes.c_double),
        ("high", ctypes.c_double),
        ("low", ctypes.c_double),
        ("close", ctypes.c_double),
        ("volume", ctypes.c_double)
    ]

class ContractMetadata(ctypes.Structure):
    _fields_ = [
        ("instrument_name", ctypes.c_char * 64),
        ("kline_count", ctypes.c_uint32),  # 原子类型，但在Python中仍然表示为uint32
        ("_padding1", ctypes.c_uint32),    # 对齐填充
        ("head_index", ctypes.c_uint32),   # 原子类型，但在Python中仍然表示为uint32
        ("_padding2", ctypes.c_uint32),    # 对齐填充
        ("last_timestamp", ctypes.c_uint64),
        ("reserved", ctypes.c_uint32 * 2)  # 保留字段减少为2个
    ]

class KlineShmHeader(ctypes.Structure):
    _fields_ = [
        ("magic", ctypes.c_uint32),
        ("version", ctypes.c_uint32),
        ("max_contracts", ctypes.c_uint32),
        ("max_klines_per_contract", ctypes.c_uint32),
        ("contract_count", ctypes.c_uint32),
        ("last_update_time", ctypes.c_uint64),
        ("reserved", ctypes.c_uint32 * 8)
    ]

# 定义完整的共享内存结构
class KlineShm(ctypes.Structure):
    _fields_ = [
        ("header", KlineShmHeader),
        ("contracts", ContractMetadata * MAX_CONTRACTS),
        ("data", (KlineData * MAX_KLINES_PER_CONTRACT) * MAX_CONTRACTS)
    ]

class KlineReader:
    def __init__(self, shm_path):
        """
        初始化K线读取器

        参数:
            shm_path: 共享内存文件路径
        """
        self.shm_path = shm_path
        self.shm = None
        self.shm_data = None
        self.open_shm()

    def open_shm(self):
        """打开共享内存文件并映射到结构体"""
        try:
            fd = os.open(self.shm_path, os.O_RDONLY)
            self.shm = mmap.mmap(fd, 0, mmap.MAP_SHARED, mmap.PROT_READ)
            os.close(fd)

            # 将共享内存映射到KlineShm结构体
            self.shm_data = KlineShm.from_buffer_copy(self.shm)

            # 验证魔数和版本
            if self.shm_data.header.magic != KLINE_SHM_MAGIC:
                raise ValueError(f"无效的共享内存格式，魔数不匹配: {self.shm_data.header.magic}")

            if self.shm_data.header.version != KLINE_SHM_VERSION:
                print(f"警告: 共享内存版本不匹配，期望 {KLINE_SHM_VERSION}，实际 {self.shm_data.header.version}")

            print(f"成功打开共享内存，包含 {self.shm_data.header.contract_count} 个合约")

        except Exception as e:
            print(f"打开共享内存失败: {e}")
            if hasattr(self, 'shm') and self.shm:
                self.shm.close()
                self.shm = None

    def get_contract_names(self):
        """获取所有合约名称"""
        if not self.shm_data:
            return []

        names = []
        for i in range(self.shm_data.header.contract_count):
            name = self.shm_data.contracts[i].instrument_name.decode('utf-8').rstrip('\0')
            names.append(name)
        return names

    def read_kline_data(self, instrument=None, fields=None):
        """
        读取K线数据 - 使用ctypes优化的版本

        参数:
            instrument: 合约名称，如果为None则读取所有合约
            fields: 需要读取的字段列表，默认为['close', 'high', 'low', 'open', 'volume']

        返回:
            一个字典，键为字段名，值为DataFrame
        """
        if not self.shm_data:
            return {}

        if fields is None:
            fields = ['close', 'high', 'low', 'open', 'volume']

        # 创建结果字典
        result = {field: {} for field in fields}

        # 确定要读取的合约列表
        contract_indices = []
        if instrument is None:
            # 读取所有合约
            for i in range(self.shm_data.header.contract_count):
                contract_indices.append(i)
        else:
            # 读取指定合约
            for i in range(self.shm_data.header.contract_count):
                name = self.shm_data.contracts[i].instrument_name.decode('utf-8').rstrip('\0')
                if name == instrument:
                    contract_indices.append(i)
                    break

        # 读取每个合约的数据
        for idx in contract_indices:
            contract = self.shm_data.contracts[idx]
            name = contract.instrument_name.decode('utf-8').rstrip('\0')

            # 创建时间索引和数据数组
            timestamps = []
            data = {field: [] for field in fields}

            # 读取K线数据
            kline_count = min(contract.kline_count, MAX_KLINES_PER_CONTRACT)

            # 预分配NumPy数组以提高性能
            if kline_count > 0:
                np_timestamps = np.zeros(kline_count, dtype=np.int64)
                np_data = {field: np.zeros(kline_count) for field in fields}

                for i in range(kline_count):
                    # 计算环形缓冲区中的实际索引
                    idx_in_buffer = (contract.head_index + i) % MAX_KLINES_PER_CONTRACT
                    kline = self.shm_data.data[idx][idx_in_buffer]

                    np_timestamps[i] = kline.timestamp

                    if 'open' in fields:
                        np_data['open'][i] = kline.open
                    if 'high' in fields:
                        np_data['high'][i] = kline.high
                    if 'low' in fields:
                        np_data['low'][i] = kline.low
                    if 'close' in fields:
                        np_data['close'][i] = kline.close
                    if 'volume' in fields:
                        np_data['volume'][i] = kline.volume

                # 转换时间戳为datetime
                # datetime_index = [datetime.fromtimestamp(ts / 1000) for ts in np_timestamps]

                # 将数据添加到结果字典
                for field in fields:
                    if field in np_data:
                        result[field][name] = pd.Series(np_data[field])

        # 将每个字段的数据转换为DataFrame
        for field in fields:
            if result[field]:
                result[field] = pd.DataFrame(result[field])

        return result

    def close(self):
        """关闭共享内存"""
        if self.shm:
            self.shm.close()
            self.shm = None
            self.shm_data = None

def main():
    parser = argparse.ArgumentParser(description='使用ctypes读取共享内存中的K线数据')
    parser.add_argument('--shm', type=str, default='/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm',
                        help='共享内存文件路径')
    parser.add_argument('--fields', type=str, default='open,close,high,low,volume',
                        help='需要读取的字段，用逗号分隔')
    parser.add_argument('--contract', type=str, default=None,
                        help='指定要读取的合约，默认读取所有合约')
    parser.add_argument('--save', action='store_true',
                        help='是否保存数据到CSV文件')
    parser.add_argument('--benchmark', action='store_true',
                        help='是否进行性能基准测试')
    parser.add_argument('--iterations', type=int, default=10,
                        help='基准测试的迭代次数')

    args = parser.parse_args()

    # 解析字段列表
    fields = args.fields.split(',')

    try:
        # 创建K线读取器
        reader = KlineReader(args.shm)

        if args.benchmark:
            # 进行性能基准测试
            print(f"进行性能基准测试，迭代次数: {args.iterations}")
            total_time = 0

            for i in range(args.iterations):
                start_time = time.time()
                data = reader.read_kline_data(args.contract, fields)
                end_time = time.time()

                iteration_time = end_time - start_time
                total_time += iteration_time

                print(f"迭代 {i+1}: {iteration_time:.6f} 秒")

            avg_time = total_time / args.iterations
            print(f"平均读取时间: {avg_time:.6f} 秒")

            # 打印数据形状
            for field in fields:
                if field in data and isinstance(data[field], pd.DataFrame) and not data[field].empty:
                    print(f"{field} 数据形状: {data[field].shape}")
        else:
            # 打印所有合约名称
            contract_names = reader.get_contract_names()
            print(f"共有 {len(contract_names)} 个合约:")
            for name in contract_names[:10]:
                print(f"  - {name}")
            if len(contract_names) > 10:
                print(f"  ... 以及其他 {len(contract_names) - 10} 个合约")

            # 读取K线数据
            start_time = time.time()
            data = reader.read_kline_data(args.contract, fields)
            end_time = time.time()
            print(f"读取耗时: {end_time - start_time:.6f} 秒")

            # 打印每个字段的数据形状
            for field in fields:
                if field in data and isinstance(data[field], pd.DataFrame) and not data[field].empty:
                    df = data[field]
                    print(f"\n{field} 数据形状: {df.shape}")
                    print(f"合约列表: {df.columns.tolist()[:5]}... (共 {len(df.columns)} 个)")
                    print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
                    print(f"样例数据:\n{df.iloc[-5:, :3]}")

                    # 如果指定了保存，将数据保存到CSV文件
                    if args.save:
                        csv_file = f"{field}_data.csv"
                        df.to_csv(csv_file)
                        print(f"数据已保存到 {csv_file}")
                else:
                    print(f"\n{field} 数据为空")

        # 关闭读取器
        reader.close()

    except Exception as e:
        print(f"读取数据时出错: {e}")

if __name__ == "__main__":
    main()
