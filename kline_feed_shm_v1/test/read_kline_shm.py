"""
读取共享内存中的K线数据
"""

import mmap
import numpy as np
import pandas as pd
from datetime import datetime
import struct
import os
import argparse

# 常量定义
KLINE_SHM_MAGIC = 0x4B4C494E  # "KLIN" in ASCII
KLINE_SHM_VERSION = 1
MAX_CONTRACTS = 1000
MAX_KLINES_PER_CONTRACT = 4000

def timestamp_to_datetime(timestamp):
    """将毫秒时间戳转换为datetime对象"""
    return datetime.fromtimestamp(timestamp / 1000)

class KlineReader:
    def __init__(self, shm_path):
        """
        初始化K线读取器

        参数:
            shm_path: 共享内存文件路径
        """
        self.shm_path = shm_path
        self.shm = None
        self.header = None
        self.contracts = []
        self.open_shm()

    def open_shm(self):
        """打开共享内存文件并读取元数据"""
        try:
            fd = os.open(self.shm_path, os.O_RDONLY)
            self.shm = mmap.mmap(fd, 0, mmap.MAP_SHARED, mmap.PROT_READ)
            os.close(fd)

            # 读取头部信息
            self.header = self._read_header()

            # 验证魔数和版本
            if self.header['magic'] != KLINE_SHM_MAGIC:
                raise ValueError(f"无效的共享内存格式，魔数不匹配: {self.header['magic']}")

            if self.header['version'] != KLINE_SHM_VERSION:
                print(f"警告: 共享内存版本不匹配，期望 {KLINE_SHM_VERSION}，实际 {self.header['version']}")

            # 读取合约信息
            self.contracts = self._read_contracts()

            print(f"成功打开共享内存，包含 {len(self.contracts)} 个合约")

        except Exception as e:
            print(f"打开共享内存失败: {e}")
            if hasattr(self, 'shm') and self.shm:
                self.shm.close()
                self.shm = None

    def _read_header(self):
        """读取共享内存头部信息"""
        header_format = "IIIIIIQ8I"  # 对应kline_shm_header结构
        header_size = struct.calcsize(header_format)
        header_data = self.shm[:header_size]

        header_values = struct.unpack(header_format, header_data)
        return {
            'magic': header_values[0],
            'version': header_values[1],
            'max_contracts': header_values[2],
            'max_klines_per_contract': header_values[3],
            'contract_count': header_values[4],
            'last_update_time': header_values[5]
        }

    def _read_contracts(self):
        """读取所有合约的元数据"""
        contracts = []
        header_size = struct.calcsize("IIIIIIQ8I")  # kline_shm_header大小
        contract_format = "64sIIIIQ2I"  # 对应修改后的contract_metadata结构（包含原子类型的填充）
        contract_size = struct.calcsize(contract_format)

        for i in range(self.header['contract_count']):
            offset = header_size + i * contract_size
            contract_data = self.shm[offset:offset + contract_size]
            values = struct.unpack(contract_format, contract_data)

            # 解析合约名称（去除空字节）
            instrument_name = values[0].decode('utf-8').rstrip('\0')

            # 注意：values[1]是kline_count，values[2]是填充，values[3]是head_index，values[4]是填充
            contracts.append({
                'instrument_name': instrument_name,
                'kline_count': values[1],
                'head_index': values[3],  # 注意这里是values[3]而不是values[2]
                'last_timestamp': values[5]  # 注意这里是values[5]而不是values[3]
            })

        return contracts

    def get_contract_names(self):
        """获取所有合约名称"""
        return [contract['instrument_name'] for contract in self.contracts]

    def read_kline_data(self, instrument=None, fields=None):
        """
        读取K线数据

        参数:
            instrument: 合约名称，如果为None则读取所有合约
            fields: 需要读取的字段列表，默认为['close', 'high', 'low', 'open', 'volume']

        返回:
            一个字典，键为字段名，值为DataFrame
        """
        if fields is None:
            fields = ['close', 'high', 'low', 'open', 'volume']

        # 创建结果字典
        result = {field: {} for field in fields}

        # 确定要读取的合约列表
        contracts_to_read = []
        if instrument is None:
            contracts_to_read = self.contracts
        else:
            for contract in self.contracts:
                if contract['instrument_name'] == instrument:
                    contracts_to_read = [contract]
                    break

        # 读取每个合约的数据
        header_size = struct.calcsize("IIIIIIQ8I")  # kline_shm_header大小
        contract_metadata_size = struct.calcsize("64sIIIIQ2I") * MAX_CONTRACTS  # 更新为新的结构体格式
        kline_format = "Qddddd"  # 对应kline_data结构
        kline_size = struct.calcsize(kline_format)

        for contract in contracts_to_read:
            # 找到合约索引
            contract_index = -1
            for i, c in enumerate(self.contracts):
                if c['instrument_name'] == contract['instrument_name']:
                    contract_index = i
                    break

            if contract_index == -1:
                continue

            # 计算K线数据的起始位置
            kline_data_offset = header_size + contract_metadata_size + \
                               (contract_index * MAX_KLINES_PER_CONTRACT * kline_size)

            # 读取K线数据
            timestamps = []
            data = {field: [] for field in fields}

            head_index = contract['head_index']
            kline_count = min(contract['kline_count'], MAX_KLINES_PER_CONTRACT)

            for i in range(kline_count):
                # 计算环形缓冲区中的实际索引
                idx = (head_index + i) % MAX_KLINES_PER_CONTRACT
                offset = kline_data_offset + idx * kline_size

                kline_bytes = self.shm[offset:offset + kline_size]
                values = struct.unpack(kline_format, kline_bytes)

                timestamp = values[0]
                timestamps.append(timestamp_to_datetime(timestamp))

                if 'open' in fields:
                    data['open'].append(values[1])
                if 'high' in fields:
                    data['high'].append(values[2])
                if 'low' in fields:
                    data['low'].append(values[3])
                if 'close' in fields:
                    data['close'].append(values[4])
                if 'volume' in fields:
                    data['volume'].append(values[5])

            # 将数据添加到结果字典
            for field in fields:
                if field in data:
                    result[field][contract['instrument_name']] = pd.Series(data[field], index=timestamps)

        # 将每个字段的数据转换为DataFrame
        for field in fields:
            if result[field]:
                result[field] = pd.DataFrame(result[field])

        return result

    def close(self):
        """关闭共享内存"""
        if self.shm:
            self.shm.close()
            self.shm = None

def main():
    parser = argparse.ArgumentParser(description='读取共享内存中的K线数据')
    parser.add_argument('--shm', type=str, default='/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm',
                        help='共享内存文件路径')
    parser.add_argument('--fields', type=str, default='close,high,volume',
                        help='需要读取的字段，用逗号分隔')
    parser.add_argument('--contract', type=str, default=None,
                        help='指定要读取的合约，默认读取所有合约')
    parser.add_argument('--save', action='store_true',
                        help='是否保存数据到CSV文件')

    args = parser.parse_args()

    # 解析字段列表
    fields = args.fields.split(',')

    try:
        # 创建K线读取器
        reader = KlineReader(args.shm)

        # 打印所有合约名称
        contract_names = reader.get_contract_names()
        print(f"共有 {len(contract_names)} 个合约:")
        for name in contract_names[:10]:
            print(f"  - {name}")
        if len(contract_names) > 10:
            print(f"  ... 以及其他 {len(contract_names) - 10} 个合约")

        # 读取K线数据
        data = reader.read_kline_data(args.contract, fields)

        # 打印每个字段的数据形状
        for field in fields:
            if field in data and isinstance(data[field], pd.DataFrame) and not data[field].empty:
                df = data[field]
                print(f"\n{field} 数据形状: {df.shape}")
                print(f"合约列表: {df.columns.tolist()[:5]}... (共 {len(df.columns)} 个)")
                print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
                print(f"样例数据:\n{df.iloc[-5:, :3]}")

                # 如果指定了保存，将数据保存到CSV文件
                if args.save:
                    csv_file = f"{field}_data.csv"
                    df.to_csv(csv_file)
                    print(f"数据已保存到 {csv_file}")
            else:
                print(f"\n{field} 数据为空")

        # 关闭读取器
        reader.close()

    except Exception as e:
        print(f"读取数据时出错: {e}")

if __name__ == "__main__":
    main()
