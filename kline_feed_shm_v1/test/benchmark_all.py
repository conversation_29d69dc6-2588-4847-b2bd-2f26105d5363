#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比不同K线数据读取方法的性能
"""

import time
import argparse
import os
import sys
import importlib.util
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def load_module(file_path, module_name):
    """从文件路径加载Python模块"""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module

def benchmark_reader(reader_class, shm_path, fields, contract=None, iterations=3):
    """对特定的读取器类进行基准测试"""
    reader = reader_class(shm_path)
    
    # 预热
    reader.read_kline_data(contract, fields)
    
    # 计时
    times = []
    for i in range(iterations):
        start_time = time.time()
        data = reader.read_kline_data(contract, fields)
        end_time = time.time()
        times.append(end_time - start_time)
        
    reader.close()
    
    # 返回时间和数据形状
    shapes = {}
    for field in fields:
        if field in data and isinstance(data[field], pd.DataFrame) and not data[field].empty:
            shapes[field] = data[field].shape
    
    return {
        'times': times,
        'avg_time': sum(times) / len(times),
        'min_time': min(times),
        'max_time': max(times),
        'shapes': shapes
    }

def main():
    parser = argparse.ArgumentParser(description='对比不同K线数据读取方法的性能')
    parser.add_argument('--shm', type=str, default='/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm',
                        help='共享内存文件路径')
    parser.add_argument('--fields', type=str, default='close,high,volume',
                        help='需要读取的字段，用逗号分隔')
    parser.add_argument('--contract', type=str, default=None,
                        help='指定要读取的合约，默认读取所有合约')
    parser.add_argument('--iterations', type=int, default=3,
                        help='每个方法的测试迭代次数')
    parser.add_argument('--plot', action='store_true',
                        help='是否绘制性能对比图')
    
    args = parser.parse_args()
    
    # 解析字段列表
    fields = args.fields.split(',')
    
    # 定义要测试的读取器模块
    reader_modules = [
        ('read_kline_shm.py', 'original'),
        ('read_kline_shm_ctypes.py', 'ctypes'),
        ('read_kline_shm_numpy.py', 'numpy'),
        ('read_kline_shm_numpy_mmap.py', 'numpy_mmap')
    ]
    
    # 存储结果
    results = {}
    
    # 测试每个读取器
    for file_name, module_name in reader_modules:
        file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file_name)
        
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            continue
        
        try:
            print(f"\n测试 {module_name} 方法...")
            module = load_module(file_path, module_name)
            reader_class = module.KlineReader
            
            result = benchmark_reader(reader_class, args.shm, fields, args.contract, args.iterations)
            results[module_name] = result
            
            print(f"平均读取时间: {result['avg_time']:.6f} 秒")
            print(f"最小读取时间: {result['min_time']:.6f} 秒")
            print(f"最大读取时间: {result['max_time']:.6f} 秒")
            
            for field, shape in result['shapes'].items():
                print(f"{field} 数据形状: {shape}")
                
        except Exception as e:
            print(f"测试 {module_name} 方法时出错: {e}")
    
    # 打印总结
    print("\n性能对比总结:")
    for name, result in sorted(results.items(), key=lambda x: x[1]['avg_time']):
        print(f"{name}: 平均 {result['avg_time']:.6f} 秒, 最小 {result['min_time']:.6f} 秒, 最大 {result['max_time']:.6f} 秒")
    
    # 绘制性能对比图
    if args.plot and results:
        plt.figure(figsize=(12, 6))
        
        # 平均时间柱状图
        plt.subplot(1, 2, 1)
        names = []
        avg_times = []
        for name, result in sorted(results.items(), key=lambda x: x[1]['avg_time']):
            names.append(name)
            avg_times.append(result['avg_time'])
        
        bars = plt.bar(names, avg_times)
        plt.title('平均读取时间对比')
        plt.ylabel('时间 (秒)')
        plt.xticks(rotation=45)
        
        # 在柱状图上添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.6f}',
                    ha='center', va='bottom', rotation=0)
        
        # 箱线图
        plt.subplot(1, 2, 2)
        box_data = []
        box_labels = []
        for name, result in sorted(results.items(), key=lambda x: x[1]['avg_time']):
            box_data.append(result['times'])
            box_labels.append(name)
        
        plt.boxplot(box_data, labels=box_labels)
        plt.title('读取时间分布')
        plt.ylabel('时间 (秒)')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig('kline_reader_benchmark.png')
        print("性能对比图已保存为 kline_reader_benchmark.png")
        plt.show()

if __name__ == "__main__":
    main()
