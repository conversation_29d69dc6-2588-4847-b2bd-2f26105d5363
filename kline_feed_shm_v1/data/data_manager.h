#pragma once
#include "../../frame/strategy_logger.h"
#include "cpp_frame/struct_serialize/ser_reflection.h"
#include "fast_trader_elite/data_model/type.h"
#include "i_strategy_ctx.h"
#include "nlohmann_json/json.hpp"
#include <cstdint>
#include <fstream>
#include <sys/types.h>
#include <vector>

#define BPS 10000
namespace fast_trader_elite::strategy {

class data {
public:
  data(fast_trader_elite::strategy::logger &logger) : logger_(logger) {}
  fast_trader_elite::exchange_type exchange_id{exchange_type::BYBIT};
  std::string instrument;
  uint16_t instrument_idx;
  double price_tick_size;
  double volume_step_size;
  int delay_cancel_time{2};

  fast_trader_elite::strategy::logger &logger_;
};

DEFINE_SER_DATA(data, instrument, instrument_idx, price_tick_size,
                volume_step_size, delay_cancel_time)

class data_manager {
public:
  data_manager(fast_trader_elite::strategy::logger &logger) : logger_(logger) {
    datas.resize(1500);
    std::fill(datas.begin(), datas.end(), nullptr);
  }
  ~data_manager() {
    for (auto &data : datas) {
      if (data) {
        delete data;
      }
    }
  }
  /*
  {
  "trading_account_id":0,
  "symbol_target":{
    "TRUMPUSDT":0.01,
    "DOGEUSDT":0.01
  }
}*/
  void init_json(nlohmann::json json) { config_json_ = json; }
  void parse_config(i_strategy_ctx *ctx) {
    std::cout << "parse_config:" << config_json_ << std::endl;
    save_path = config_json_["save_path"].get<std::string>();
    kline_cnt = config_json_["kline_cnt"].get<int>();
    auto all_ins = ctx->get_all_instruments(exchange_type::BYBIT);
    for (auto ins : all_ins) {
      auto cur_data = new data(logger_);
      datas[ins->instrument_idx] = cur_data;
      cur_data->instrument_idx = ins->instrument_idx;
      cur_data->instrument = ins->instrument_name;
      cur_data->price_tick_size = ins->tick_size;
      cur_data->volume_step_size = ins->step_size;
      // STRA_LOG(logger_, STRA_INFO, "config:{}",
      //          cpp_frame::struct_serialize::to_json(*cur_data));
    }
  }

public:
  std::string save_path;
  int kline_cnt{4500};
  fast_trader_elite::exchange_type exchange_id{exchange_type::BYBIT};
  std::vector<data *> datas;
  fast_trader_elite::strategy::logger &logger_;

private:
  nlohmann::json config_json_;
};
} // namespace fast_trader_elite::strategy
