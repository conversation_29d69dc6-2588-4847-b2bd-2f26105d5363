CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o: \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/main.cc \
 /usr/include/stdc-predef.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/../strategy/strategy.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/nlohmann_json/json.hpp \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/algorithm \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/utility \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/c++config.h \
 /usr/include/bits/wordsize.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/os_defines.h \
 /usr/include/features.h /usr/include/sys/cdefs.h \
 /usr/include/bits/long-double.h /usr/include/gnu/stubs.h \
 /usr/include/gnu/stubs-64.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/cpu_defines.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/pstl/pstl_config.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_relops.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_pair.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/move.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/type_traits \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/compare \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/concepts \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/initializer_list \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ext/numeric_traits.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/cpp_type_traits.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ext/type_traits.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_algobase.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/functexcept.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/exception_defines.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_iterator_base_types.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/iterator_concepts.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ptr_traits.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ranges_cmp.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_iterator_base_funcs.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/concept_check.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/debug/assertions.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_iterator.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/new \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/exception.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/debug/debug.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/predefined_ops.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_algo.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cstdlib \
 /usr/include/stdlib.h /usr/include/bits/libc-header-start.h \
 /opt/rh/gcc-toolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include/stddef.h \
 /usr/include/bits/waitflags.h /usr/include/bits/waitstatus.h \
 /usr/include/bits/floatn.h /usr/include/bits/floatn-common.h \
 /usr/include/bits/types/locale_t.h /usr/include/bits/types/__locale_t.h \
 /usr/include/sys/types.h /usr/include/bits/types.h \
 /usr/include/bits/typesizes.h /usr/include/bits/types/clock_t.h \
 /usr/include/bits/types/clockid_t.h /usr/include/bits/types/time_t.h \
 /usr/include/bits/types/timer_t.h /usr/include/bits/stdint-intn.h \
 /usr/include/endian.h /usr/include/bits/endian.h \
 /usr/include/bits/byteswap.h /usr/include/bits/uintn-identity.h \
 /usr/include/sys/select.h /usr/include/bits/select.h \
 /usr/include/bits/types/sigset_t.h /usr/include/bits/types/__sigset_t.h \
 /usr/include/bits/types/struct_timeval.h \
 /usr/include/bits/types/struct_timespec.h \
 /usr/include/bits/pthreadtypes.h /usr/include/bits/thread-shared-types.h \
 /usr/include/bits/pthreadtypes-arch.h /usr/include/alloca.h \
 /usr/include/bits/stdlib-bsearch.h /usr/include/bits/stdlib-float.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/std_abs.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/algorithmfwd.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_heap.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_tempbuf.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_construct.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/uniform_int_dist.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ranges_algo.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ranges_algobase.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/iterator \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/iosfwd \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stringfwd.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/memoryfwd.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/postypes.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cwchar \
 /usr/include/wchar.h \
 /opt/rh/gcc-toolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include/stdarg.h \
 /usr/include/bits/wchar.h /usr/include/bits/types/wint_t.h \
 /usr/include/bits/types/mbstate_t.h \
 /usr/include/bits/types/__mbstate_t.h /usr/include/bits/types/__FILE.h \
 /usr/include/bits/types/FILE.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stream_iterator.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/streambuf_iterator.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/streambuf \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/localefwd.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/c++locale.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/clocale \
 /usr/include/locale.h /usr/include/bits/locale.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cctype \
 /usr/include/ctype.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ios_base.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ext/atomicity.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/gthr.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h /usr/include/bits/sched.h \
 /usr/include/bits/types/struct_sched_param.h /usr/include/bits/cpu-set.h \
 /usr/include/time.h /usr/include/bits/time.h /usr/include/bits/timex.h \
 /usr/include/bits/types/struct_tm.h \
 /usr/include/bits/types/struct_itimerspec.h /usr/include/bits/setjmp.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/atomic_word.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/locale_classes.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/string \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/char_traits.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cstdint \
 /opt/rh/gcc-toolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include/stdint.h \
 /usr/include/stdint.h /usr/include/bits/stdint-uintn.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/allocator.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/c++allocator.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ext/new_allocator.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ostream_insert.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/cxxabi_forced.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_function.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/backward/binders.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/range_access.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/basic_string.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ext/alloc_traits.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/alloc_traits.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/string_view \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/functional_hash.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/hash_bytes.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ranges_base.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/max_size_type.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/numbers \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/string_view.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ext/string_conversions.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cstdio \
 /usr/include/stdio.h /usr/include/bits/types/__fpos_t.h \
 /usr/include/bits/types/__fpos64_t.h \
 /usr/include/bits/types/struct_FILE.h \
 /usr/include/bits/types/cookie_io_functions_t.h \
 /usr/include/bits/stdio_lim.h /usr/include/bits/sys_errlist.h \
 /usr/include/bits/stdio.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cerrno \
 /usr/include/errno.h /usr/include/bits/errno.h \
 /usr/include/linux/errno.h /usr/include/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/bits/types/error_t.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/charconv.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/basic_string.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/locale_classes.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/system_error \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/error_constants.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/stdexcept \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/exception \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/exception_ptr.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/cxxabi_init_exception.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/typeinfo \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/nested_exception.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/streambuf.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/invoke.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ranges_util.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/pstl/glue_algorithm_defs.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/functional \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tuple \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/array \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/uses_allocator.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/refwrap.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/std_function.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/unordered_map \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ext/aligned_buffer.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/hashtable.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/hashtable_policy.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/enable_special_members.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/node_handle.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/unordered_map.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/erase_if.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/vector \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_uninitialized.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_vector.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_bvector.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/vector.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/pstl/execution_defs.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cstddef \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/memory \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_raw_storage_iter.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/align.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bit \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/unique_ptr.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ostream \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ios \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/basic_ios.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/locale_facets.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cwctype \
 /usr/include/wctype.h /usr/include/bits/wctype-wchar.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/ctype_base.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/ctype_inline.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/locale_facets.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/basic_ios.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ostream.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/shared_ptr.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/shared_ptr_base.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/allocated_ptr.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ext/concurrence.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/shared_ptr_atomic.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/atomic_base.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/atomic_lockfree_defines.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/atomic_wait.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/climits \
 /opt/rh/gcc-toolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include/limits.h \
 /opt/rh/gcc-toolset-11/root/usr/lib/gcc/x86_64-redhat-linux/11/include/syslimits.h \
 /usr/include/limits.h /usr/include/bits/posix1_lim.h \
 /usr/include/bits/local_lim.h /usr/include/linux/limits.h \
 /usr/include/bits/posix2_lim.h /usr/include/bits/xopen_lim.h \
 /usr/include/bits/uio_lim.h /usr/include/unistd.h \
 /usr/include/bits/posix_opt.h /usr/include/bits/environments.h \
 /usr/include/bits/confname.h /usr/include/bits/getopt_posix.h \
 /usr/include/bits/getopt_core.h /usr/include/syscall.h \
 /usr/include/sys/syscall.h /usr/include/asm/unistd.h \
 /usr/include/asm/unistd_64.h /usr/include/bits/syscall.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/std_mutex.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/backward/auto_ptr.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/ranges_uninitialized.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/uses_allocator_args.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/pstl/glue_memory_defs.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/numeric \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_numeric.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/limits \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/pstl/glue_numeric_defs.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/forward_list \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/forward_list.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/forward_list.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/map \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_tree.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_map.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_multimap.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/valarray \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cmath /usr/include/math.h \
 /usr/include/bits/math-vector.h /usr/include/bits/libm-simd-decl-stubs.h \
 /usr/include/bits/flt-eval-method.h /usr/include/bits/fp-logb.h \
 /usr/include/bits/fp-fast.h \
 /usr/include/bits/mathcalls-helper-functions.h \
 /usr/include/bits/mathcalls.h /usr/include/bits/mathcalls-narrow.h \
 /usr/include/bits/iscanonical.h /usr/include/bits/mathinline.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/specfun.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/gamma.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/special_function_util.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/bessel_function.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/beta_function.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/ell_integral.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/exp_integral.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/hypergeometric.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/legendre_function.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/modified_bessel_func.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/poly_hermite.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/poly_laguerre.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/tr1/riemann_zeta.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/valarray_array.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/valarray_array.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/valarray_before.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/slice_array.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/valarray_after.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/gslice.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/gslice_array.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/mask_array.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/indirect_array.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/version \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cassert \
 /usr/include/assert.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/cstring \
 /usr/include/string.h /usr/include/strings.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/filesystem \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/fs_fwd.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/chrono \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ratio \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ctime \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/parse_numbers.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/fs_path.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/locale \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/locale_facets_nonio.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/time_members.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/messages_members.h \
 /usr/include/libintl.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/codecvt.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/locale_facets_nonio.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/locale_conv.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/iomanip \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/quoted_string.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/sstream \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/istream \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/istream.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/sstream.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/codecvt \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/fs_dir.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/fs_ops.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/ranges \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/optional \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/any \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/queue \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/deque \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_deque.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/deque.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_queue.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/unordered_set \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/unordered_set.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/../strategy/../../frame/strategy_logger.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/async_log.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/async_log_impl.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include/fmt/format.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include/fmt/core.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include/fmt/format.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include/fmt/format-inl.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/mutex \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/unique_lock.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/async_log_macro.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/file_helper.h \
 /usr/include/sys/stat.h /usr/include/bits/stat.h \
 /usr/include/bits/statx.h /usr/include/linux/stat.h \
 /usr/include/linux/types.h /usr/include/asm/types.h \
 /usr/include/asm-generic/types.h /usr/include/asm-generic/int-ll64.h \
 /usr/include/asm/bitsperlong.h /usr/include/asm-generic/bitsperlong.h \
 /usr/include/linux/posix_types.h /usr/include/linux/stddef.h \
 /usr/include/asm/posix_types.h /usr/include/asm/posix_types_64.h \
 /usr/include/asm-generic/posix_types.h /usr/include/bits/statx-generic.h \
 /usr/include/bits/types/struct_statx_timestamp.h \
 /usr/include/bits/types/struct_statx.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include/fmt/core.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/fmt_async.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/log_helper.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/string_literal.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/log_buffer.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/async_logger/nanots.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/iostream \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/fstream \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/basic_file.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/x86_64-redhat-linux/bits/c++io.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/fstream.tcc \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include/fmt/args.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api/i_strategy_ctx.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include/fast_trader_elite/data_model/field.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/struct_serialize/struct_ser.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/struct_serialize/ser_reflection.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/variant \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include/fast_trader_elite/data_model/type.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/../strategy/../data/data_manager.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/../strategy/../data/shm_kline_manager.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/atomic \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/shm/shm_util.h \
 /usr/include/fcntl.h /usr/include/bits/fcntl.h \
 /usr/include/bits/fcntl-linux.h /usr/include/bits/types/struct_iovec.h \
 /usr/include/linux/falloc.h /usr/include/sys/mman.h \
 /usr/include/bits/mman.h /usr/include/bits/mman-linux.h \
 /usr/include/bits/mman-shared.h /usr/include/sys/shm.h \
 /usr/include/sys/ipc.h /usr/include/bits/ipctypes.h \
 /usr/include/bits/ipc.h /usr/include/bits/shm.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/regex \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bitset \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/stack \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/stl_stack.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_constants.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_error.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_automaton.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_automaton.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_scanner.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_scanner.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_compiler.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_compiler.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex.tcc \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_executor.h \
 /opt/rh/gcc-toolset-11/root/usr/include/c++/11/bits/regex_executor.tcc \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api/i_strategy.h \
 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include/cpp_frame/utils/file_helper.h
