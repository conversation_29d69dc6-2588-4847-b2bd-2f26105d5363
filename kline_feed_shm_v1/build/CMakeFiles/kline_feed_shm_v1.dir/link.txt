/opt/rh/gcc-toolset-11/root/usr/bin/c++ -fPIC  -std=c++20 -O3 -g -Wall -msse4 -fPIC -shared -Wl,-soname,libkline_feed_shm_v1.so -o libkline_feed_shm_v1.so CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o  -lpthread -ldl -lstdc++fs -lrt -lfmt -ldpp
