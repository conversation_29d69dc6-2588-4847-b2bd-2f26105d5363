# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build

# Include any dependencies generated for this target.
include CMakeFiles/kline_feed_shm_v1.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/kline_feed_shm_v1.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/kline_feed_shm_v1.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/kline_feed_shm_v1.dir/flags.make

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o: CMakeFiles/kline_feed_shm_v1.dir/flags.make
CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o: /home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc
CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o: CMakeFiles/kline_feed_shm_v1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o -MF CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o.d -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o -c /home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc > CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.i

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.s

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o: CMakeFiles/kline_feed_shm_v1.dir/flags.make
CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o: /home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc
CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o: CMakeFiles/kline_feed_shm_v1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o -MF CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o.d -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o -c /home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc > CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.i

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.s

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o: CMakeFiles/kline_feed_shm_v1.dir/flags.make
CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o: /home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc
CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o: CMakeFiles/kline_feed_shm_v1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o -MF CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o.d -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o -c /home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc > CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.i

CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.s

CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o: CMakeFiles/kline_feed_shm_v1.dir/flags.make
CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o: /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/shm_kline_manager.cc
CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o: CMakeFiles/kline_feed_shm_v1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o -MF CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o.d -o CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o -c /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/shm_kline_manager.cc

CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/shm_kline_manager.cc > CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.i

CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/shm_kline_manager.cc -o CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.s

CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o: CMakeFiles/kline_feed_shm_v1.dir/flags.make
CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o: /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/main.cc
CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o: CMakeFiles/kline_feed_shm_v1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o -MF CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o.d -o CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o -c /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/main.cc

CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/main.cc > CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.i

CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/main.cc -o CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.s

CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o: CMakeFiles/kline_feed_shm_v1.dir/flags.make
CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o: /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/strategy.cc
CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o: CMakeFiles/kline_feed_shm_v1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o -MF CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o.d -o CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o -c /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/strategy.cc

CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/strategy.cc > CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.i

CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/strategy.cc -o CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.s

# Object files for target kline_feed_shm_v1
kline_feed_shm_v1_OBJECTS = \
"CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o" \
"CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o" \
"CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o" \
"CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o" \
"CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o" \
"CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o"

# External object files for target kline_feed_shm_v1
kline_feed_shm_v1_EXTERNAL_OBJECTS =

libkline_feed_shm_v1.so: CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o
libkline_feed_shm_v1.so: CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o
libkline_feed_shm_v1.so: CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o
libkline_feed_shm_v1.so: CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o
libkline_feed_shm_v1.so: CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o
libkline_feed_shm_v1.so: CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o
libkline_feed_shm_v1.so: CMakeFiles/kline_feed_shm_v1.dir/build.make
libkline_feed_shm_v1.so: CMakeFiles/kline_feed_shm_v1.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX shared library libkline_feed_shm_v1.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/kline_feed_shm_v1.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/kline_feed_shm_v1.dir/build: libkline_feed_shm_v1.so
.PHONY : CMakeFiles/kline_feed_shm_v1.dir/build

CMakeFiles/kline_feed_shm_v1.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/kline_feed_shm_v1.dir/cmake_clean.cmake
.PHONY : CMakeFiles/kline_feed_shm_v1.dir/clean

CMakeFiles/kline_feed_shm_v1.dir/depend:
	cd /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1 /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/kline_feed_shm_v1.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/kline_feed_shm_v1.dir/depend

