# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# compile CXX with /opt/rh/gcc-toolset-11/root/usr/bin/c++
CXX_DEFINES = -DFMT_HEADER_ONLY -Dkline_feed_shm_v1_EXPORTS

CXX_INCLUDES = -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty

CXX_FLAGS =  -std=c++20 -O3 -g -Wall -msse4 -fPIC -fPIC

