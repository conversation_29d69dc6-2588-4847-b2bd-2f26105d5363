# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.29
cmake_policy(SET CMP0009 NEW)

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../frame/executor/*.cc")
set(OLD_GLOB
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../frame/executor/executor_manager.cc"
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../frame/executor/position_executor.cc"
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../frame/executor/rate_limiter.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/*.cc")
set(OLD_GLOB
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/shm_kline_manager.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/*.h")
set(OLD_GLOB
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/data_manager.h"
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/shm_kline_manager.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/*.cc")
set(OLD_GLOB
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/main.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/*.h")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/rule/*.cc")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/rule/*.h")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/*.cc")
set(OLD_GLOB
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/strategy.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()

# srcs at CMakeLists.txt:7 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/*.h")
set(OLD_GLOB
  "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/strategy.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/cmake.verify_globs")
endif()
