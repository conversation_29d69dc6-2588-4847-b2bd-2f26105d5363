# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -P /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/VerifyGlobs.cmake
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named kline_feed_shm_v1

# Build rule for target.
kline_feed_shm_v1: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 kline_feed_shm_v1
.PHONY : kline_feed_shm_v1

# fast build rule for target.
kline_feed_shm_v1/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/build
.PHONY : kline_feed_shm_v1/fast

data/shm_kline_manager.o: data/shm_kline_manager.cc.o
.PHONY : data/shm_kline_manager.o

# target to build an object file
data/shm_kline_manager.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o
.PHONY : data/shm_kline_manager.cc.o

data/shm_kline_manager.i: data/shm_kline_manager.cc.i
.PHONY : data/shm_kline_manager.i

# target to preprocess a source file
data/shm_kline_manager.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.i
.PHONY : data/shm_kline_manager.cc.i

data/shm_kline_manager.s: data/shm_kline_manager.cc.s
.PHONY : data/shm_kline_manager.s

# target to generate assembly for a file
data/shm_kline_manager.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.s
.PHONY : data/shm_kline_manager.cc.s

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.o: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.o
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.o

# target to build an object file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.o

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.i: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.i
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.i

# target to preprocess a source file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.i
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.i

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.s: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.s
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.s

# target to generate assembly for a file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.s
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.cc.s

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.o: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.o
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.o

# target to build an object file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.o

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.i: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.i
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.i

# target to preprocess a source file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.i
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.i

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.s: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.s
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.s

# target to generate assembly for a file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.s
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.cc.s

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.o: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.o

# target to build an object file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.i: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.i
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.i

# target to preprocess a source file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.i
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.i

home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.s: home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.s
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.s

# target to generate assembly for a file
home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.s
.PHONY : home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.cc.s

main/main.o: main/main.cc.o
.PHONY : main/main.o

# target to build an object file
main/main.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o
.PHONY : main/main.cc.o

main/main.i: main/main.cc.i
.PHONY : main/main.i

# target to preprocess a source file
main/main.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.i
.PHONY : main/main.cc.i

main/main.s: main/main.cc.s
.PHONY : main/main.s

# target to generate assembly for a file
main/main.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.s
.PHONY : main/main.cc.s

strategy/strategy.o: strategy/strategy.cc.o
.PHONY : strategy/strategy.o

# target to build an object file
strategy/strategy.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o
.PHONY : strategy/strategy.cc.o

strategy/strategy.i: strategy/strategy.cc.i
.PHONY : strategy/strategy.i

# target to preprocess a source file
strategy/strategy.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.i
.PHONY : strategy/strategy.cc.i

strategy/strategy.s: strategy/strategy.cc.s
.PHONY : strategy/strategy.s

# target to generate assembly for a file
strategy/strategy.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kline_feed_shm_v1.dir/build.make CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.s
.PHONY : strategy/strategy.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... kline_feed_shm_v1"
	@echo "... data/shm_kline_manager.o"
	@echo "... data/shm_kline_manager.i"
	@echo "... data/shm_kline_manager.s"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.o"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.i"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/executor_manager.s"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.o"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.i"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/position_executor.s"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.o"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.i"
	@echo "... home/fast_trader_elite/git/cross_selction_strategy/frame/executor/rate_limiter.s"
	@echo "... main/main.o"
	@echo "... main/main.i"
	@echo "... main/main.s"
	@echo "... strategy/strategy.o"
	@echo "... strategy/strategy.i"
	@echo "... strategy/strategy.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -P /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build/CMakeFiles/VerifyGlobs.cmake
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

