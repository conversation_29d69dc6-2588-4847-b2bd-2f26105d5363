[{"directory": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -DFMT_HEADER_ONLY -Dkline_feed_shm_v1_EXPORTS -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty  -std=c++20 -O3 -g -Wall -msse4 -fPIC -fPIC -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o -c /home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc", "file": "/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc", "output": "CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/executor_manager.cc.o"}, {"directory": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -DFMT_HEADER_ONLY -Dkline_feed_shm_v1_EXPORTS -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty  -std=c++20 -O3 -g -Wall -msse4 -fPIC -fPIC -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o -c /home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc", "file": "/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc", "output": "CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/position_executor.cc.o"}, {"directory": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -DFMT_HEADER_ONLY -Dkline_feed_shm_v1_EXPORTS -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty  -std=c++20 -O3 -g -Wall -msse4 -fPIC -fPIC -o CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o -c /home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc", "file": "/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc", "output": "CMakeFiles/kline_feed_shm_v1.dir/home/<USER>/git/cross_selction_strategy/frame/executor/rate_limiter.cc.o"}, {"directory": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -DFMT_HEADER_ONLY -Dkline_feed_shm_v1_EXPORTS -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty  -std=c++20 -O3 -g -Wall -msse4 -fPIC -fPIC -o CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o -c /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/shm_kline_manager.cc", "file": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/data/shm_kline_manager.cc", "output": "CMakeFiles/kline_feed_shm_v1.dir/data/shm_kline_manager.cc.o"}, {"directory": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -DFMT_HEADER_ONLY -Dkline_feed_shm_v1_EXPORTS -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty  -std=c++20 -O3 -g -Wall -msse4 -fPIC -fPIC -o CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o -c /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/main.cc", "file": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/main/main.cc", "output": "CMakeFiles/kline_feed_shm_v1.dir/main/main.cc.o"}, {"directory": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -DFMT_HEADER_ONLY -Dkline_feed_shm_v1_EXPORTS -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/data_model/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../fast_trader_elite/api -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty/fmt/include -I/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/../../cpp_frame/thirdparty  -std=c++20 -O3 -g -Wall -msse4 -fPIC -fPIC -o CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o -c /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/strategy.cc", "file": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/strategy/strategy.cc", "output": "CMakeFiles/kline_feed_shm_v1.dir/strategy/strategy.cc.o"}]