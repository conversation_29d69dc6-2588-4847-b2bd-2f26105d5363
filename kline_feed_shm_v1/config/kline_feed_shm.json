{"common": {"log_dir": "../log/shm_kline_feed_fast_trader_elite.log", "global_instrument_config_path": "../config/instrument_config/global_ins_map.json"}, "instrument_config": [{"exchange": "bybit", "instrument_idx_config": "../config/instrument_config/bybit_ins_map.json", "instrument_info_config": "../config/instrument_config/bybit_future_infos.json"}], "md": {"log_level": "debug", "md_names": ["bybit", "bybit_1", "bybit_2", "bybit_3", "bybit_4", "bybit_5", "bybit_6", "bybit_7", "bybit_8", "bybit_9"], "okex": {"exchange": "okex", "so_path": "../md_so_plugin/libokex_md_proxy_plugin.so"}, "binance": {"exchange": "binance", "so_path": "../md_so_plugin/libbinance_md_proxy_plugin.so"}, "bybit": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_1": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_2": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_3": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_4": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_5": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_6": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_7": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_8": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}, "bybit_9": {"exchange": "bybit", "so_path": "../md_so_plugin/libbybit_md_proxy_plugin.so"}}, "td": {"log_level": "trace", "td_names": [], "binance": {"so_path": "../td_so_plugin/libbinance_td_proxy_plugin.so", "local_id_prefix": 100000, "exchange": "binance", "trading_account_id": 0, "secret_key": "", "api_key": "", "password": ""}, "bybit": {"so_path": "../td_so_plugin/libbybit_td_proxy_plugin.so", "local_id_prefix": 100000, "exchange": "bybit", "trading_account_id": 0, "secret_key": "", "api_key": "", "password": ""}, "bybit1": {"so_path": "../td_so_plugin/libbybit_td_proxy_plugin.so", "sub_code": [], "local_id_prefix": 300000, "exchange": "bybit", "trading_account_id": 1, "secret_key": "", "api_key": "", "password": ""}}, "strategy": {"log_level": "all", "so_path": "../strategy_plugin/", "async_thread_interval": 1}, "strategy_info": {"strategy_names": ["kline_feed_shm"], "kline_feed_shm": {"so_path": "../strategy_plugin/libkline_feed_shm.so", "log_level": "DEBUG", "log_file": "../log/kline_feed_shm.log", "strategy_config_path": "../strategy_config/strategy_kline_feed_shm.json"}}}