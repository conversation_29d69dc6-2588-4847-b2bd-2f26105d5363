#include "kline_data_reader.hpp"
#include <cstring>
#include <cmath>

KlineDataReader::KlineDataReader(const std::string &shm_path, int aggregation_minutes, int offset_minutes)
    : shm_path_(shm_path), shm_ptr_(nullptr), last_known_close_time_(0),
      aggregation_minutes_(aggregation_minutes), offset_minutes_(offset_minutes) {}

KlineDataReader::~KlineDataReader() {
  if (shm_ptr_) {
    // 计算实际的共享内存大小
    size_t shm_size = sizeof(kline_shm_header) +
                      sizeof(contract_metadata) * MAX_CONTRACTS +
                      sizeof(kline_data) * MAX_CONTRACTS * shm_ptr_->header.max_klines_per_contract;
    cpp_frame::shm::release_mmap_buffer(shm_ptr_, shm_size);
  }
}

bool KlineDataReader::initialize() {
  // 加载共享内存（使用较大的默认大小）
  shm_ptr_ = cpp_frame::shm::load_mmap_buffer<kline_shm>(shm_path_.c_str(), false);
  if (!shm_ptr_) {
    std::cerr << "Failed to load shared memory: " << shm_path_ << std::endl;
    return false;
  }

  // 验证共享内存格式
  if (!validate_shm_format()) {
    return false;
  }

  // 初始化已知的最后更新时间
  last_known_close_time_ = shm_ptr_->header.last_kline_close_time;

  std::cout << "✓ 共享内存初始化成功" << std::endl;
  std::cout << "  路径: " << shm_path_ << std::endl;
  std::cout << "  合约数量: " << shm_ptr_->header.contract_count << std::endl;
  std::cout << "  聚合周期: " << aggregation_minutes_ << " 分钟" << std::endl;
  std::cout << "  偏移量: " << offset_minutes_ << " 分钟" << std::endl;
  std::cout << "  最后K线收盘时间: " << format_timestamp(last_known_close_time_) << std::endl;

  return true;
}

bool KlineDataReader::validate_shm_format() {
  if (!shm_ptr_) {
    std::cerr << "共享内存未初始化" << std::endl;
    return false;
  }

  // 验证魔数
  if (shm_ptr_->header.magic != KLINE_SHM_MAGIC) {
    std::cerr << "无效的共享内存格式，魔数不匹配: 0x" << std::hex
              << shm_ptr_->header.magic << std::dec << std::endl;
    return false;
  }

  // 验证版本
  if (shm_ptr_->header.version != KLINE_SHM_VERSION) {
    std::cout << "警告: 共享内存版本不匹配，期望 " << KLINE_SHM_VERSION
              << "，实际 " << shm_ptr_->header.version << std::endl;
  }

  return true;
}

bool KlineDataReader::has_data_updated() {
  if (!shm_ptr_) {
    return false;
  }

  uint64_t current_close_time = shm_ptr_->header.last_kline_close_time;
  if (current_close_time != last_known_close_time_) {
    last_known_close_time_ = current_close_time;
    return true;
  }

  return false;
}

uint64_t KlineDataReader::get_last_kline_close_time() const {
  if (!shm_ptr_) {
    return 0;
  }
  return shm_ptr_->header.last_kline_close_time;
}

std::vector<std::string> KlineDataReader::get_contract_names() {
  std::vector<std::string> names;
  if (!shm_ptr_) {
    return names;
  }

  for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
    std::string name(shm_ptr_->contracts[i].instrument_name);
    // 移除字符串末尾的空字符
    size_t pos = name.find('\0');
    if (pos != std::string::npos) {
      name = name.substr(0, pos);
    }
    names.push_back(name);
  }

  return names;
}

std::vector<ContractKlineData> KlineDataReader::get_all_contracts_data() {
  std::vector<ContractKlineData> all_data;
  if (!shm_ptr_) {
    return all_data;
  }

  for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
    const contract_metadata &metadata = shm_ptr_->contracts[i];

    ContractKlineData contract_data;
    contract_data.instrument_name = std::string(metadata.instrument_name);
    // 移除字符串末尾的空字符
    size_t pos = contract_data.instrument_name.find('\0');
    if (pos != std::string::npos) {
      contract_data.instrument_name = contract_data.instrument_name.substr(0, pos);
    }

    // 读取原始1分钟K线数据并聚合
    std::vector<kline_data> raw_klines = read_contract_raw_klines(i, metadata);
    std::vector<kline_data> aggregated = aggregate_klines_get_snap_style(raw_klines);

    contract_data.klines = aggregated;
    contract_data.last_timestamp = metadata.last_timestamp;
    contract_data.kline_count = aggregated.size();

    all_data.push_back(contract_data);
  }

  return all_data;
}

ContractKlineData
KlineDataReader::get_contract_data(const std::string &instrument_name) {
  ContractKlineData contract_data;
  if (!shm_ptr_) {
    return contract_data;
  }

  // 查找指定合约
  for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
    const contract_metadata &metadata = shm_ptr_->contracts[i];
    std::string name(metadata.instrument_name);
    size_t pos = name.find('\0');
    if (pos != std::string::npos) {
      name = name.substr(0, pos);
    }

    if (name == instrument_name) {
      contract_data.instrument_name = name;

      // 读取原始1分钟K线数据并聚合
      std::vector<kline_data> raw_klines = read_contract_raw_klines(i, metadata);
      std::vector<aggregated_kline_data> aggregated = aggregate_klines(raw_klines);

      contract_data.klines = aggregated;
      contract_data.last_timestamp = metadata.last_timestamp;
      contract_data.kline_count = aggregated.size();
      break;
    }
  }

  return contract_data;
}

std::vector<kline_data>
KlineDataReader::read_contract_raw_klines(uint32_t contract_index,
                                          const contract_metadata &metadata) {
  std::vector<kline_data> klines;

  uint32_t kline_count = metadata.kline_count.load(std::memory_order_acquire);
  uint32_t head_index = metadata.head_index.load(std::memory_order_acquire);

  if (kline_count == 0) {
    return klines;
  }

  // 读取K线数据
  uint32_t actual_count = std::min(kline_count, shm_ptr_->header.max_klines_per_contract);
  klines.reserve(actual_count);

  for (uint32_t i = 0; i < actual_count; ++i) {
    uint32_t data_index;
    if (kline_count < shm_ptr_->header.max_klines_per_contract) {
      // 缓冲区未满，从头开始读
      data_index = i;
    } else {
      // 缓冲区已满，从head_index开始读（环形缓冲区）
      data_index = (head_index + i) % shm_ptr_->header.max_klines_per_contract;
    }

    kline_data* kline_ptr = get_kline_data_ptr(contract_index, data_index);
    if (kline_ptr) {
      klines.push_back(*kline_ptr);
    }
  }

  return klines;
}

void KlineDataReader::print_summary() {
  if (!shm_ptr_) {
    std::cout << "共享内存未初始化" << std::endl;
    return;
  }

  std::cout << "\n=== 共享内存数据摘要 ===" << std::endl;
  std::cout << "合约数量: " << shm_ptr_->header.contract_count << std::endl;
  std::cout << "最后K线收盘时间: "
            << format_timestamp(shm_ptr_->header.last_kline_close_time)
            << std::endl;
  std::cout << "最后更新时间戳: " << shm_ptr_->header.last_update_timestamp
            << std::endl;

  std::cout << "\n合约列表:" << std::endl;
  for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
    const contract_metadata &metadata = shm_ptr_->contracts[i];
    uint32_t kline_count = metadata.kline_count.load(std::memory_order_acquire);

    std::cout << "  " << metadata.instrument_name
              << " - K线数量: " << kline_count
              << ", 最后时间: " << format_timestamp(metadata.last_timestamp)
              << std::endl;
  }
}

std::vector<ContractKlineData>
KlineDataReader::get_historical_data(int num_klines) {
  std::vector<ContractKlineData> historical_data;
  if (!shm_ptr_) {
    return historical_data;
  }

  for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
    const contract_metadata &metadata = shm_ptr_->contracts[i];

    ContractKlineData contract_data;
    contract_data.instrument_name = std::string(metadata.instrument_name);
    // 移除字符串末尾的空字符
    size_t pos = contract_data.instrument_name.find('\0');
    if (pos != std::string::npos) {
      contract_data.instrument_name = contract_data.instrument_name.substr(0, pos);
    }

    // 读取历史原始K线数据并聚合
    std::vector<kline_data> raw_klines = read_contract_historical_raw_klines(i, metadata, num_klines * aggregation_minutes_);
    std::vector<aggregated_kline_data> aggregated = aggregate_klines(raw_klines);

    // 只保留最后num_klines个聚合K线
    if (aggregated.size() > static_cast<size_t>(num_klines)) {
      aggregated.erase(aggregated.begin(), aggregated.end() - num_klines);
    }

    contract_data.klines = aggregated;
    contract_data.last_timestamp = metadata.last_timestamp;
    contract_data.kline_count = aggregated.size();

    historical_data.push_back(contract_data);
  }

  return historical_data;
}

std::vector<ContractKlineData> KlineDataReader::get_latest_data() {
  std::vector<ContractKlineData> latest_data;
  if (!shm_ptr_) {
    return latest_data;
  }

  for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
    const contract_metadata &metadata = shm_ptr_->contracts[i];

    ContractKlineData contract_data;
    contract_data.instrument_name = std::string(metadata.instrument_name);
    // 移除字符串末尾的空字符
    size_t pos = contract_data.instrument_name.find('\0');
    if (pos != std::string::npos) {
      contract_data.instrument_name = contract_data.instrument_name.substr(0, pos);
    }

    // 获取最新的一些原始K线数据并聚合（获取足够的数据以形成最新的聚合K线）
    std::vector<kline_data> raw_klines = read_contract_historical_raw_klines(i, metadata, aggregation_minutes_ * 2);
    std::vector<aggregated_kline_data> aggregated = aggregate_klines(raw_klines);

    // 只保留最新的一个聚合K线
    if (!aggregated.empty()) {
      contract_data.klines.push_back(aggregated.back());
    }

    contract_data.last_timestamp = metadata.last_timestamp;
    contract_data.kline_count = contract_data.klines.size();

    latest_data.push_back(contract_data);
  }

  return latest_data;
}

std::vector<kline_data> KlineDataReader::read_contract_historical_raw_klines(
    uint32_t contract_index, const contract_metadata &metadata,
    int num_klines) {
  std::vector<kline_data> klines;

  uint32_t kline_count = metadata.kline_count.load(std::memory_order_acquire);
  uint32_t head_index = metadata.head_index.load(std::memory_order_acquire);

  if (kline_count == 0) {
    return klines;
  }

  // 计算要读取的K线数量（不超过实际数量和请求数量）
  uint32_t max_klines = shm_ptr_->header.max_klines_per_contract;
  uint32_t actual_count = std::min({kline_count, max_klines, static_cast<uint32_t>(num_klines)});
  klines.reserve(actual_count);

  // 计算起始位置（从最后num_klines个开始读取）
  uint32_t start_offset = 0;
  if (kline_count > static_cast<uint32_t>(num_klines)) {
    start_offset = kline_count - num_klines;
  }

  for (uint32_t i = start_offset; i < kline_count && i < start_offset + num_klines; ++i) {
    uint32_t data_index;
    if (kline_count < max_klines) {
      // 缓冲区未满，从头开始读
      data_index = i;
    } else {
      // 缓冲区已满，从head_index开始读（环形缓冲区）
      data_index = (head_index + i) % max_klines;
    }

    kline_data* kline_ptr = get_kline_data_ptr(contract_index, data_index);
    if (kline_ptr) {
      klines.push_back(*kline_ptr);
    }
  }

  return klines;
}

std::string KlineDataReader::format_timestamp(uint64_t timestamp_ms) {
  if (timestamp_ms == 0)
    return "N/A";

  time_t time_sec = timestamp_ms / 1000;
  struct tm *time_info = localtime(&time_sec);

  char buffer[32];
  strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", time_info);
  return std::string(buffer);
}

void KlineDataReader::set_aggregation_params(int aggregation_minutes, int offset_minutes) {
  aggregation_minutes_ = aggregation_minutes;
  offset_minutes_ = offset_minutes;

  // 清空缓存，因为聚合参数改变了
  aggregated_cache_.clear();
  last_processed_timestamp_.clear();
}

std::vector<aggregated_kline_data> KlineDataReader::aggregate_klines(const std::vector<kline_data> &raw_klines) {
  std::vector<aggregated_kline_data> result;
  if (raw_klines.empty()) {
    return result;
  }

  // 按时间窗口分组聚合
  std::map<uint64_t, std::vector<kline_data>> time_windows;

  for (const auto& kline : raw_klines) {
    uint64_t window_start = calculate_aggregation_window_start(kline.timestamp);
    time_windows[window_start].push_back(kline);
  }

  // 对每个时间窗口进行聚合
  for (const auto& [window_start, window_klines] : time_windows) {
    if (window_klines.empty()) continue;

    aggregated_kline_data agg_kline;
    agg_kline.timestamp = window_start;
    agg_kline.open = window_klines.front().open;
    agg_kline.close = window_klines.back().close;
    agg_kline.volume = 0.0;
    agg_kline.count = window_klines.size();

    // 计算最高价、最低价和总成交量
    agg_kline.high = window_klines[0].high;
    agg_kline.low = window_klines[0].low;

    for (const auto& kline : window_klines) {
      agg_kline.high = std::max(agg_kline.high, kline.high);
      agg_kline.low = std::min(agg_kline.low, kline.low);
      agg_kline.volume += kline.volume;
    }

    result.push_back(agg_kline);
  }

  // 按时间排序
  std::sort(result.begin(), result.end(),
            [](const aggregated_kline_data& a, const aggregated_kline_data& b) {
              return a.timestamp < b.timestamp;
            });

  return result;
}

uint64_t KlineDataReader::calculate_aggregation_window_start(uint64_t timestamp) {
  // 将时间戳转换为分钟
  uint64_t timestamp_minutes = timestamp / (60 * 1000);

  // 应用偏移量
  uint64_t adjusted_minutes = timestamp_minutes - offset_minutes_;

  // 计算聚合窗口的开始分钟
  uint64_t window_start_minutes = (adjusted_minutes / aggregation_minutes_) * aggregation_minutes_ + offset_minutes_;

  // 转换回毫秒时间戳
  return window_start_minutes * 60 * 1000;
}

kline_data* KlineDataReader::get_kline_data_ptr(uint32_t contract_index, uint32_t kline_index) {
  if (!shm_ptr_ || contract_index >= shm_ptr_->header.contract_count) {
    return nullptr;
  }

  // 计算在柔性数组中的偏移量
  size_t offset = contract_index * shm_ptr_->header.max_klines_per_contract + kline_index;
  return &(shm_ptr_->data[offset]);
}
