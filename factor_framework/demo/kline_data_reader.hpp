#pragma once

#include "shm_util.h"
#include <atomic>
#include <ctime>
#include <iomanip>
#include <iostream>
#include <memory>
#include <sstream>
#include <string>
#include <unordered_map>
#include <vector>

// 共享内存相关常量和结构定义（兼容1分钟K线SHM文件）
constexpr uint32_t KLINE_SHM_MAGIC = 0x4B4C494E; // "KLIN" in ASCII
constexpr uint32_t KLINE_SHM_VERSION = 1;
constexpr uint32_t MAX_CONTRACTS = 1000;           // 最大合约数量
constexpr uint32_t MAX_KLINES_PER_CONTRACT = 13000; // 每个合约的最大K线数量（1分钟数据更多）

// 共享内存头部结构（兼容实际SHM文件格式）
struct kline_shm_header {
  uint32_t magic;                   // 魔数，用于验证共享内存格式
  uint32_t version;                 // 版本号
  uint32_t max_contracts;           // 最大合约数量
  uint32_t max_klines_per_contract; // 每个合约的最大K线数量
  uint32_t contract_count;          // 当前合约数量
  uint64_t last_kline_close_time;   // 最后更新时间 这一轮K线闭合更新 K线收盘时间
  uint64_t last_update_timestamp;   // 最后更新时间戳
  uint32_t program_state;           // 程序状态
  uint32_t reserved[5];             // 保留字段，用于未来扩展
};

// 合约元数据结构
struct contract_metadata {
  char instrument_name[64]; // 合约名称
  alignas(4)
      std::atomic<uint32_t> kline_count; // 当前K线数量，使用原子操作保证可见性
  alignas(4) std::atomic<
      uint32_t> head_index; // 环形缓冲区头部索引，使用原子操作保证可见性
  uint64_t last_timestamp; // 最后更新时间戳 合约K线收盘时间
  uint32_t reserved[2]; // 保留字段（减少为2个以保持结构大小不变）
};

// K线数据结构
struct kline_data {
  uint64_t timestamp; // 时间戳（毫秒）
  double open;        // 开盘价
  double high;        // 最高价
  double low;         // 最低价
  double close;       // 收盘价
  double volume;      // 成交量
};

// 共享内存总体结构（使用柔性数组，兼容实际SHM文件）
struct kline_shm {
  kline_shm_header header;                    // 头部信息
  contract_metadata contracts[MAX_CONTRACTS]; // 合约元数据数组
  kline_data data[];                          // K线数据柔性数组
};

// 单个合约的K线数据
struct ContractKlineData {
  std::string instrument_name;
  std::vector<kline_data> klines;
  uint64_t last_timestamp;
  uint32_t kline_count;
};

/**
 * K线数据读取器
 * 负责从共享内存中读取1分钟K线数据并聚合成指定周期的K线数据
 *
 * 功能特性：
 * 1. 从1分钟SHM数据聚合成10分钟K线（可配置）
 * 2. 支持offset参数控制聚合时间点（如offset=1时，在01,11,21分钟聚合）
 * 3. 聚合逻辑：open取窗口第一个，high/low取窗口最值，close取窗口最后一个，volume求和
 * 4. 兼容现有的DataConverter和FactorFramework接口
 */
class KlineDataReader {
public:
  KlineDataReader(const std::string &shm_path,
                  int aggregation_minutes = 10,
                  int offset_minutes = 0);
  ~KlineDataReader();

  // 初始化共享内存连接
  bool initialize();

  // 检查数据是否有更新
  bool has_data_updated();

  // 获取最新的K线收盘时间
  uint64_t get_last_kline_close_time() const;

  // 获取所有合约的聚合K线数据
  std::vector<ContractKlineData> get_all_contracts_data();

  // 获取指定合约的聚合K线数据
  ContractKlineData get_contract_data(const std::string &instrument_name);

  // 获取历史数据用于初始化（最后N个聚合K线）
  std::vector<ContractKlineData> get_historical_data(int num_klines = 1200);

  // 获取最新的一行数据（用于增量更新）
  std::vector<ContractKlineData> get_latest_data();

  // 获取合约列表
  std::vector<std::string> get_contract_names();

  // 打印调试信息
  void print_summary();

  // 时间戳格式化
  static std::string format_timestamp(uint64_t timestamp_ms);

  // 设置聚合参数
  void set_aggregation_params(int aggregation_minutes, int offset_minutes);

private:
  std::string shm_path_;
  kline_shm *shm_ptr_;
  uint64_t last_known_close_time_;
  int aggregation_minutes_;  // 聚合周期（分钟）
  int offset_minutes_;       // 偏移量（分钟）

  // 验证共享内存格式
  bool validate_shm_format();

  // 读取单个合约的原始1分钟K线数据
  std::vector<kline_data> read_contract_raw_klines(uint32_t contract_index,
                                                   const contract_metadata &metadata);

  // 读取单个合约的历史原始K线数据（最后N个）
  std::vector<kline_data> read_contract_historical_raw_klines(uint32_t contract_index,
                                                              const contract_metadata &metadata,
                                                              int num_klines);

  // 聚合1分钟K线数据为指定周期的K线
  std::vector<kline_data> aggregate_klines(const std::vector<kline_data> &raw_klines);

  // 检查时间戳是否符合聚合条件（基于offset）
  bool is_aggregation_point(uint64_t timestamp_ms);

  // 获取K线数据指针（处理柔性数组）
  kline_data* get_kline_data_ptr(uint32_t contract_index, uint32_t kline_index);
};
